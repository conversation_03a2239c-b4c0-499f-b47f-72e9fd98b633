#!/bin/bash

# ---
# Профессиональный скрипт для комплексной проверки Python-проекта.
# Поддерживает режимы: check (для pre-commit) и fix (для разработки).
# По умолчанию использует параллельные проверки для максимальной скорости.
# Использование: ./check_code.sh [check|fix] [--sequential]
# ---

set -uo pipefail  # убрал -e для возможности собрать все ошибки

# Цвета для вывода
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly RED='\033[0;31m'
readonly NC='\033[0m'

# Конфигурация
readonly MODE="${1:-check}"
readonly PARALLEL_MODE="${2:-parallel}"  # по умолчанию параллельно
readonly FAIL_FAST="${FAIL_FAST:-true}"

# Массивы для правильной обработки путей
readonly -a PROJECT_DIRS=(app tools)
readonly -a ROOT_PATTERNS=(run_*.py)
readonly -a EXCLUDE_DIRS=(__pycache__ .venv .pytest_cache tmp)

# Глобальные счетчики
TOTAL_ERRORS=0
CHECKS_FAILED=()

# Функции для логирования
log_info() { echo -e "${YELLOW}$1${NC}"; }
log_success() { echo -e "${GREEN}$1${NC}"; }
log_error() { echo -e "${RED}$1${NC}" >&2; }

# Сбор Python файлов для проверки
get_python_files() {
    local files=()
    
    # Собираем файлы из директорий
    for dir in "${PROJECT_DIRS[@]}"; do
        [[ -d "$dir" ]] && files+=("$dir")
    done
    
    # Добавляем корневые файлы по паттернам
    for pattern in "${ROOT_PATTERNS[@]}"; do
        while IFS= read -r -d '' file; do
            files+=("$file")
        done < <(find . -maxdepth 1 -name "$pattern" -type f -print0 2>/dev/null)
    done
    
    printf '%s\n' "${files[@]}"
}

# Формирование exclude параметров
build_exclude_args() {
    local args=()
    for dir in "${EXCLUDE_DIRS[@]}"; do
        args+=("--exclude=$dir")
    done
    printf '%s\n' "${args[@]}"
}

# Проверка установленных инструментов
check_dependencies() {
    local -a missing_tools=()
    local -a required_tools=(ruff mypy bandit)
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Отсутствуют инструменты: ${missing_tools[*]}"
        log_error "Установите их: pip install -r requirements-dev.txt"
        exit 1
    fi
}

# Универсальная функция для запуска проверок
run_check() {
    local name="$1"
    local check_cmd="$2"
    local fix_cmd="${3:-}"
    
    log_info "\n[$name] Начало проверки..."
    
    local exit_code=0
    if [[ "$MODE" == "fix" && -n "$fix_cmd" ]]; then
        eval "$fix_cmd" || exit_code=$?
    else
        eval "$check_cmd" || exit_code=$?
    fi
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "[$name] OK"
    else
        log_error "[$name] FAILED"
        CHECKS_FAILED+=("$name")
        ((TOTAL_ERRORS++))
        [[ "$FAIL_FAST" == "true" ]] && exit 1
    fi
    
    return $exit_code
}

# Форматирование кода
run_format() {
    local files
    mapfile -t files < <(get_python_files)
    [[ ${#files[@]} -eq 0 ]] && return 0
    
    local exclude_args
    mapfile -t exclude_args < <(build_exclude_args)
    
    local check_cmd="ruff format --check ${files[*]} ${exclude_args[*]}"
    local fix_cmd="ruff format ${files[*]} ${exclude_args[*]}"
    
    run_check "Format" "$check_cmd" "$fix_cmd"
}

# Линтинг кода
run_lint() {
    local files
    mapfile -t files < <(get_python_files)
    [[ ${#files[@]} -eq 0 ]] && return 0
    
    local exclude_args
    mapfile -t exclude_args < <(build_exclude_args)
    
    local check_cmd="ruff check ${files[*]} ${exclude_args[*]}"
    local fix_cmd="ruff check --fix ${files[*]} ${exclude_args[*]}"
    
    run_check "Lint" "$check_cmd" "$fix_cmd"
}

# Проверка типов
run_type_check() {
    # Проверяем наличие mypy.ini или pyproject.toml с настройками mypy
    local mypy_config=""
    [[ -f "mypy.ini" ]] && mypy_config="--config-file mypy.ini"
    [[ -f "pyproject.toml" ]] && grep -q "\[tool.mypy\]" pyproject.toml && mypy_config=""
    
    # Используем настройки для современной типизации
    local mypy_flags="--ignore-missing-imports --no-strict-optional $mypy_config"
    
    local check_cmd="mypy ${PROJECT_DIRS[*]} $mypy_flags"
    
    run_check "Type Check" "$check_cmd"
}

# Проверка безопасности
run_security_check() {
    local files
    mapfile -t files < <(get_python_files)
    [[ ${#files[@]} -eq 0 ]] && return 0
    
    # Более гибкая настройка bandit
    local bandit_flags="-ll --skip B101"  # B101 - assert_used
    local check_cmd="bandit -r ${files[*]} $bandit_flags"
    
    run_check "Security" "$check_cmd"
}

# Запуск тестов (опционально)
run_tests() {
    if [[ -d "tests" ]] && command -v pytest >/dev/null 2>&1; then
        log_info "\n[Tests] Запуск pytest..."
        local test_cmd="pytest tests/ -xvs --tb=short"
        run_check "Tests" "$test_cmd"
    fi
}


# Проверка импортов и мертвого кода
run_dead_code_check() {
    if command -v vulture >/dev/null 2>&1; then
        log_info "\n[Dead Code] Поиск неиспользуемого кода..."
        
        # Запускаем vulture и захватываем вывод
        local output
        local exit_code=0
        output=$(vulture app tools --min-confidence 90 --sort-by-size 2>&1) || exit_code=$?
        
        # Vulture всегда возвращает 0, даже если найдены проблемы
        # Проверяем наличие вывода - если есть вывод, значит найден мертвый код
        if [[ -n "$output" ]]; then
            echo "$output"
            log_error "[Dead Code] FAILED"
            CHECKS_FAILED+=("Dead Code")
            ((TOTAL_ERRORS++))
            [[ "$FAIL_FAST" == "true" ]] && exit 1
        else
            log_success "[Dead Code] OK"
        fi
    else
        # Альтернатива через ruff для неиспользуемых импортов
        log_info "\n[Dead Code] Проверка неиспользуемых импортов через ruff..."
        local files
        mapfile -t files < <(get_python_files)
        [[ ${#files[@]} -eq 0 ]] && return 0
        
        local exclude_args
        mapfile -t exclude_args < <(build_exclude_args)
        
        local cmd="ruff check --select F401,F841 ${files[*]} ${exclude_args[*]}"
        run_check "Dead Code" "$cmd"
    fi
}

# Проверка уязвимостей в зависимостях
run_vulnerability_check() {
    # Сначала пробуем pip-audit как более стабильный
    if command -v pip-audit >/dev/null 2>&1; then
        log_info "\n[Vulnerabilities] Проверка через pip-audit..."
        
        local output
        local exit_code=0
        
        # pip-audit работает стабильнее и не требует API ключей
        output=$(timeout 60 pip-audit --desc --format=json 2>&1) || exit_code=$?
        
        if [[ $exit_code -eq 0 ]]; then
            # Проверяем наличие уязвимостей в JSON
            if echo "$output" | grep -q '"vulnerabilities": \[\]' || echo "$output" | grep -q "No known vulnerabilities found"; then
                log_success "[Vulnerabilities] OK"
            else
                echo "Найдены уязвимости:"
                echo "$output" | grep -A3 -B1 '"id":'
                log_error "[Vulnerabilities] FAILED"
                CHECKS_FAILED+=("Vulnerabilities")
                ((TOTAL_ERRORS++))
                [[ "$FAIL_FAST" == "true" ]] && exit 1
            fi
        elif [[ $exit_code -eq 124 ]]; then
            log_info "[Vulnerabilities] pip-audit timeout - пропускаем проверку"
        else
            echo "$output"
            log_error "[Vulnerabilities] pip-audit failed with exit code $exit_code"
            CHECKS_FAILED+=("Vulnerabilities")
            ((TOTAL_ERRORS++))
            [[ "$FAIL_FAST" == "true" ]] && exit 1
        fi
        
    elif command -v safety >/dev/null 2>&1; then
        log_info "\n[Vulnerabilities] Проверка через safety (fallback)..."
        
        local output
        local exit_code=0
        
        # Проверяем есть ли API ключ для safety
        if [[ -n "${SAFETY_API_KEY:-}" ]] || safety --version >/dev/null 2>&1; then
            # Используем более простую команду без JSON для избежания проблем
            output=$(timeout 45 safety scan --short-report 2>&1) || exit_code=$?
            
            if [[ $exit_code -eq 0 ]]; then
                if echo "$output" | grep -q "No known security vulnerabilities found"; then
                    log_success "[Vulnerabilities] OK"
                else
                    echo "$output"
                    log_error "[Vulnerabilities] FAILED"
                    CHECKS_FAILED+=("Vulnerabilities")
                    ((TOTAL_ERRORS++))
                    [[ "$FAIL_FAST" == "true" ]] && exit 1
                fi
            elif [[ $exit_code -eq 124 ]]; then
                log_info "[Vulnerabilities] safety timeout - пропускаем проверку"
            else
                # Проверяем если это ошибка API ключа
                if echo "$output" | grep -q -i "api.*key\|authentication\|login"; then
                    log_info "[Vulnerabilities] safety требует API ключ - пропускаем"
                else
                    echo "$output"
                    log_error "[Vulnerabilities] safety failed with exit code $exit_code"
                    CHECKS_FAILED+=("Vulnerabilities")
                    ((TOTAL_ERRORS++))
                    [[ "$FAIL_FAST" == "true" ]] && exit 1
                fi
            fi
        else
            log_info "[Vulnerabilities] safety требует настройки или API ключ - пропускаем"
        fi
        
    else
        log_info "\n[Vulnerabilities] Инструменты не установлены (pip-audit/safety) - пропускаем"
        log_info "Для установки: pip install pip-audit  # или safety"
    fi
}




# Параллельный запуск проверок (УЛУЧШАЕМ - добавляем новые проверки)
run_parallel_checks() {
    log_info "🚀 Запуск проверок в параллельном режиме..."
    
    local -a pids=()
    local -a check_names=()
    
    # Основные проверки (критичные)
    run_format & pids+=($!); check_names+=("Format")
    run_lint & pids+=($!); check_names+=("Lint")
    run_type_check & pids+=($!); check_names+=("Type Check")
    run_security_check & pids+=($!); check_names+=("Security")
    
    # Дополнительные проверки (полезные, но не критичные)
    run_dead_code_check & pids+=($!); check_names+=("Dead Code")
    run_vulnerability_check & pids+=($!); check_names+=("Vulnerabilities")
    
    # Ждем завершения всех процессов
    local failed=0
    for i in "${!pids[@]}"; do
        if ! wait "${pids[$i]}"; then
            ((failed++))
            log_error "[${check_names[$i]}] завершился с ошибкой"
        fi
    done
    
    return $failed
}

# Последовательный запуск (УЛУЧШАЕМ - добавляем новые проверки)
run_sequential_checks() {
    log_info "📋 Последовательный режим (для отладки)"
    
    # Основные проверки
    run_format
    run_lint
    run_type_check
    run_security_check
    
    # Дополнительные проверки (быстрые сначала)
    run_dead_code_check      # Быстрая проверка мертвого кода
    run_vulnerability_check  # Медленная проверка уязвимостей
    
    # Опционально тесты
    run_tests
}

# Основная функция (ИСПРАВЛЯЕМ - правильный вызов последовательных проверок)
main() {
    case "$MODE" in
        check|fix) ;;
        *)
            log_error "Неизвестный режим: $MODE. Используйте 'check' или 'fix'"
            exit 1
            ;;
    esac
    
    log_info "--- Проверка Python-проекта [режим: $MODE] ---"
    
    # Проверяем зависимости
    check_dependencies
    
    # Проверяем наличие файлов для анализа
    local files
    mapfile -t files < <(get_python_files)
    if [[ ${#files[@]} -eq 0 ]]; then
        log_error "Не найдены Python файлы для проверки"
        exit 1
    fi
    
    log_info "Найдено файлов/директорий для проверки: ${#files[@]}"
    
    # Выполняем проверки
    if [[ "$PARALLEL_MODE" == "--sequential" || "$PARALLEL_MODE" == "--no-parallel" ]]; then
        run_sequential_checks
    else
        run_parallel_checks
    fi
    
    # Итоговый отчет
    echo
    if [[ $TOTAL_ERRORS -eq 0 ]]; then
        log_success "✅ Все проверки успешно пройдены!"
        exit 0
    else
        log_error "❌ Обнаружено ошибок: $TOTAL_ERRORS"
        log_error "Проваленные проверки: ${CHECKS_FAILED[*]}"
        exit 1
    fi
}

# Обработка сигналов для корректного завершения
trap 'kill $(jobs -p) 2>/dev/null' EXIT

# Запуск
main "$@"

# Использование:
# # Обычная проверка (по умолчанию параллельно, ~4x быстрее)
# ./check_code.sh check

# # Исправление проблем (параллельно)
# ./check_code.sh fix

# # Последовательная проверка (для отладки ошибок)
# ./check_code.sh check --sequential

# # Показать все ошибки (не останавливаться на первой)
# FAIL_FAST=false ./check_code.sh check
