# run_10_scan_sources.py

import argparse
import logging

import redis
from app import settings
from app.ingestion.scanner_inventorizer import ScannerInventorizer
from app.storage import LocalStorageManager, S3StorageManager


def main():
    parser = argparse.ArgumentParser(
        description="Сканер-инвентаризатор источников книг (новая версия)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:

  Стандартное сканирование (рекомендуется):
    python run_10_scan_sources.py

  Тихий режим для cron:
    python run_10_scan_sources.py --quiet

  Сканирование с S3 хранилищем:
    python run_10_scan_sources.py --storage s3

  Восстановление SET активных задач:
    python run_10_scan_sources.py --rebuild-set

ВАЖНО: Новая версия сканера НЕ использует SET_PROCESSED кэш.
PostgreSQL (book_sources) является единственным источником правды.
        """,
    )

    parser.add_argument(
        "--storage",
        choices=["local", "s3"],
        default="local",
        help="Тип хранилища: local (локальная ФС) или s3 (S3 хранилище)",
    )

    parser.add_argument(
        "--rebuild-set",
        action="store_true",
        help="Восстановить SET активных задач из очередей Redis",
    )

    parser.add_argument(
        "--quiet",
        "-q",
        action="store_true",
        help="Тихий режим - только критические ошибки",
    )

    args = parser.parse_args()

    # Настраиваем логирование
    # Отключаем варнинги psycopg о rollback соединений
    logging.getLogger("psycopg.pool").setLevel(logging.ERROR)
    logging.getLogger("psycopg").setLevel(logging.ERROR)

    if args.quiet:
        logging.basicConfig(level=logging.WARNING, format="%(message)s")
    else:
        logging.basicConfig(
            level=logging.INFO,
            format="%(message)s",  # Убираем timestamp для читаемости
        )

    try:
        if args.rebuild_set:
            logging.info("🔄 Восстановление SET активных задач...")
            from app.processing.queue_manager import rebuild_queued_set

            redis_client = redis.from_url(settings.REDIS_URL)
            rebuild_queued_set(redis_client)
            logging.info("✅ SET активных задач восстановлен")
            return 0

        # Создаем менеджер хранилища
        if args.storage == "s3":
            # Для S3 можно добавить параметры из ENV
            storage_manager = S3StorageManager()
            logging.info("🌐 Используется S3 хранилище")
        else:
            storage_manager = LocalStorageManager()
            logging.info("💾 Используется локальная файловая система")

        # Создаем и запускаем новый сканер
        scanner = ScannerInventorizer(storage_manager=storage_manager)
        stats = scanner.scan_all_sources()

        # Проверяем результат
        if stats.errors > 0:
            logging.warning(f"⚠️ Сканирование завершено с ошибками: {stats.errors}")
            return 1

        if stats.new_tasks_created == 0:
            logging.info("📭 Новых задач для обработки не найдено")

        return 0

    except KeyboardInterrupt:
        logging.info("🛑 Сканирование прервано пользователем")
        return 130
    except Exception as e:
        logging.critical(f"💥 Критическая ошибка при сканировании: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
