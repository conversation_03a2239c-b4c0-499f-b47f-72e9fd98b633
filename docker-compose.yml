# --- File: ./docker-compose.yml ---
services:
  db:
    image: postgres:17.5-alpine
    container_name: ${PROJECT_NAME:-postgres}_db
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-postgres}
    volumes:
      # Используем именованный том. Docker сам позаботится о правах.
      # - ${PROJECT_NAME}_data:/var/lib/postgresql/data/
      
      # Сохраняем данные БД на хост-машине, чтобы они не удалялись при перезапуске контейнера
      - ${POSTGRES_DATA_PATH:?Переменная POSTGRES_DATA_PATH не задана в .env файле! Укажите путь для хранения данных БД.}:/var/lib/postgresql/data/
      
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

# Определяем именованный том. Его имя будет "books_data"
# volumes:
#   ${PROJECT_NAME}_data: