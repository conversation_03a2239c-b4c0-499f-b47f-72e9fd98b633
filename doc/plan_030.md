# Этап 2: RAG Pipeline — детальный план   (⏱ 2–3 месяц<PERSON>, MVP)

## Шаг 2.0 Kick-off (1 неделя)
• **Действия:** финальное ревью ТЗ, спринтовая декомпозиция, назначение ответственных.  
• **Ответственные:** Product-owner (PO), Tech Lead (RAG), Scrum-master.  
• **Ресурсы:** 4-часовой воркшоп, Confluence, Jira.  
• Критерий успеха: backlog, road-map Gantt, согласованные OKR.  
• **Риски:** рассинхронизация ожиданий → митигируем – PO фиксирует scope-freeze.

## Шаг 2.1 Аппаратная и DevOps-инфраструктура (1–2 нед.)
• Действия:  
  1. Поднять тестовый кластер Qdrant (Docker Swarm/K8s).  
  2. Выделить GPU-нод (1×A100 или эквивалент) с CUDA 12.  
  3. Настроить GitLab CI «rag-pipeline» со stage build/test/deploy.  
• Ответственные: DevOps Engineer.  
• Ресурсы: 64 GB RAM, 2 TB NVMe, GPU node, бюджет $0.6/час.  
• Критерий успеха: готовый helm-release Qdrant + CI-job проходит «healthcheck /ready».  
• Риски: нехватка GPU-квот → запасной план – аренда runpod.io.

## Шаг 2.2 Схема хранилища эмбеддингов (1 нед.)
• Действия: описать коллекцию `books_chunks` (vector = 1536, distance = cosine, payload: book_id, chunk_id, pos, lang) и индексы.  
• Ответственные: DBA + ML Engineer.  
• Критерий успеха: `qdrant_client.collection_info` — статус `green`.  
• Риски: schema-drift при миграциях → внедрить Alembic-like миграции для Qdrant.

## Шаг 2.3 Чанкер‐микросервис (2–3 нед.)
• Технологии: LangChain (python), FastAPI, pydantic-v2.  
• Паттерн «pipeline filter + map-reduce».  
• Действия:  
  1. `BookChunker.chunk(book_text)` → list[ChunkDTO].  
  2. Поддержать адаптивный размер (≈ 500 токенов) + языковое определение.  
• Ответственные: Python Engineer (2).  
• Ресурсы: CPU-node, 4 vCPU.  
• Критерий успеха: 10 книг/мин, unit-coverage ≥ 85 %, json-schema стабильна.  
• Риски: слишком длинные предложения ломают токенизацию → эвристика «предложение > 300 симв. — мягкий split».

## Шаг 2.4 Embedding-Service + шина очередей (3 нед.)
• Действия:  
  1. gRPC-служба `Embedder` (Python + NVIDIA Triton).  
  2. Очередь `rag:queue:new` в Redis, работа по батчам 128 chunk'ов.  
  3. Пакетная запись `points_upsert` в Qdrant.  
• Ответственные: ML Engineer, Backend-Engineer.  
• Ресурсы: GPU node, NVMe для batched LMDB cache.  
• Критерий успеха: Throughput ≥ 4K embeddings/sec, latency P95 < 250 ms.  
• Риски: GPU-OOM → градиентное сжатие, half-precision.

## Шаг 2.5 Entity + Tagging LLM-pipeline (параллельный поток, 2 нед.)
• Действия: prompt-engineering для extraction, сохранение в таблицы `book_entities`, `book_tags`.  
• Модель: mixtral-8x7b-instr @ 8-bit.  
• Ответственные: NLP Engineer.  
• Критерий успеха: согласованность ≥ 0.85 (BLEU на golden-sample 100 книг).  
• Риски: галлюцинации LLM → пост-валидация правилами Pydantic.

## Шаг 2.6 Интеграция с текущим Ingestion-Pipeline (1 нед.)
• Действия:  
  1. В `BookProcessor` после сохранения в БД кидаем задачу в `rag:queue:new`.  
  2. Метрики Prometheus: `books_processed_total`, `chunks_embedded_total`.  
• Ответственные: Core Backend-Dev.  
• Критерий успеха: end-to-end поток «ZIP → Redis → PostgreSQL → Qdrant» без ручного вмешательства.  
• Риски: блокировка Redis из-за больших батчей → лимит 5k tasks и back-pressure.

## Шаг 2.7 QA и приемка (1 нед.)
• Тест-план: нагрузочное (10k книг, 2 млн чанков), функциональное, chaos-tests Qdrant-node-kill.  
• Критерии «Done»:  
  – 0 данных-сирот между PostgreSQL / Qdrant;  
  – восстановление за < 10 минут при падении сервиса;  
  – поиск `«космический пират с чувством юмора»` возвращает релевант ≥ 0.7 MRR на тестовой выборке.  

### Документы/разделы, регулирующие шаг
• Roadmap «Будущие этапы → RAG Pipeline» ```135:142:doc/roadmap.md```  
• Passport «Подсистема анализа и обогащения контента (RAG Pipeline)» ```150:167:doc/passport.md```  
• Architecture «📖 Семантические элементы для RAG» ```50:60:doc/architecture/parsing_architecture.md```

### Риски общего уровня и их минимизация
1. Дефицит GPU → ранний резерв в облаке, fallback-режим CPU + smaller-model.  
2. Расхождение схем PostgreSQL/Qdrant → миграции через миграционный слой с checksum.  
3. Всплеск затрат на хранение векторов → компрессия Product Quantization, shard-rotation.  
4. Качество эмбеддингов < порога → A/B на двух моделях (e5-large vs text-embedding-ada-002).
