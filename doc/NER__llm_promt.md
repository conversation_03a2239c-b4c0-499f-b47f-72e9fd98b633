# NER промт для обогащения чанка при векторизации
--- v1.3
{
  "role": "system",
  "content": "You are a literary analyst and data architect. Your task is to enrich a text fragment with metadata to improve vector search. Extract information ONLY from the provided fragment. Avoid speculation. Your response must be ONLY a valid JSON object, starting with `{` and ending with `}`. All string values must be in Russian."
}
{
  "role": "user",
  "content": "Analyze the text fragment below.

Text to analyze:
---
[СЮДА ВСТАВИТЬ ТЕКСТ ЧАНКА]
---

JSON output format:
{
  "chunk_summary": "string // Краткое содержание фрагмента в одном предложении.",
  "entities": [
    { "name": "string", "type": "string // ENUM: ['Person', 'Location', 'Organization', 'Object']", "status": "string // ENUM: ['Active', 'Mentioned']" }
  ],
  "key_topics": ["string"],
  "atmosphere": ["string"],
  "subtext": "string // Раскрой скрытую информацию: мотивы, чувства, символизм, предзнаменования. Если подтекста нет, используй null."
}
"
}

# Извлекаем данные из JSONB поля
ner_data = row['ner_metadata']
text_to_vectorize = (
    row['original_text'] + " " + 
    ner_data.get('chunk_summary', '') + " " + 
    ner_data.get('subtext', '')
)

# Эти данные пойдут в payload/metadata вашего вектора
vector_metadata = {
    "book_id": row['book_id'],
    "chunk_id": row['chunk_id'],
    "entities": ner_data.get('entities', []),
    "key_topics": ner_data.get('key_topics', []),
    "atmosphere": ner_data.get('atmosphere', [])
}

--- v1.1
Your task is to extract the MOST IMPORTANT entities from a text fragment for a vector database. Your response must be ONLY a valid JSON object.

Rules:
1. Be extremely selective. Extract ONLY the top 2-3 most central entities for each category.
2. Do not list every mentioned entity. Focus on who is ACTING or being discussed the most.
3. All values in the JSON output MUST be in Russian.

Text to analyze:
---
[СЮДА ВСТАВЛЯЕТСЯ ТЕКСТ ЧАНКА НА РУССКОМ]
---

JSON output format:
{
  "central_actors": ["string"],
  "primary_locations": ["string"],
  "key_topics": ["string"]
}
