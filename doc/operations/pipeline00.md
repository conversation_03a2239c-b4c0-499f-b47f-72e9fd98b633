# Pipeline 00: Система аварийного восстановления

```bash
# Полное восстановление системы
python run_00_recovery.py

# Непрерывный мониторинг (запуск в фоне)
python run_00_recovery.py --monitor

# Проверка статуса очередей
redis-cli LLEN books:queue:new
redis-cli LLEN books:queue:processing
redis-cli LLEN books:queue:completed
```

## 📖 Назначение

Система аварийного восстановления `run_00_recovery.py` — инструмент для диагностики и восстановления целостности системы обработки книг. Её задачи:

1. **Восстановление зависших задач** из очередей Redis

> **📋 Документация очередей**: См. [../redis_queues.md](../redis_queues.md) для полного описания архитектуры очередей Redis.
2. **Поиск потерянных файлов** в директориях `/in_progress/`
3. **Очистка поврежденных записей** в Redis очередях
4. **Проверка целостности данных** в PostgreSQL
5. **Непрерывный мониторинг** состояния системы

## 🏗️ Архитектура восстановления

```
      СИСТЕМА ОБРАБОТКИ КНИГ
┌─────────────────────────────────────────┐
│         Redis (3 очереди)               │
│  ┌─────────────────────────────────────┐ │
│  │ books:queue:new (новые)             │ │
│  │ books:queue:processing (в работе)   │ │ ──┐
│  │ books:queue:completed (завершенные) │ │   │
│  └─────────────────────────────────────┘ │   │
└─────────────────────────────────────────┘   │
                                              │
      ФАЙЛОВАЯ СИСТЕМА                        │ RECOVERY
┌─────────────────────────────────────────┐   │ АНАЛИЗИРУЕТ
│ /source/                (исходные)      │   │ И ВОССТАНАВЛИВАЕТ
│ /in_progress/           (в работе)      │ ──┤
│ /processed/             (завершенные)   │   │
│ /quarantine/            (проблемные)    │   │
└─────────────────────────────────────────┘   │
                                              │
      POSTGRESQL                              │
┌─────────────────────────────────────────┐   │
│ book_sources (обработанные файлы)       │ ──┘
│ books (метаданные книг)                 │
│ authors, series (связанные данные)      │
└─────────────────────────────────────────┘
```

## 🔄 Типы восстановления

### 1️⃣ Восстановление зависших задач

**Проблема:** Задачи "застряли" в `books:queue:processing` из-за сбоев воркера.

**Алгоритм:**
```
1. Найти задачи старше WORKER_TIMEOUT (300 сек)
2. Проверить состояние соответствующих файлов
3. Если файл в /in_progress/ → вернуть в /source/ и в books:queue:new
4. Если файл в /source/ → удалить задачу из processing
5. Если файл не найден → удалить задачу (считать потерянным)
```

**Результат:** Зависшие задачи возвращаются для повторной обработки.

### 2️⃣ Поиск потерянных файлов

**Проблема:** Файлы остались в `/in_progress/` без соответствующих задач в очередях.

**Алгоритм (обновлен - Dec 2024):**
```
1. Сканировать все /in_progress/ директории
2. Для каждого файла определить source_type по родительской директории:
   - zip_flibusta/in_progress/12345.zip → source_type=1
   - zip_searchfloor/in_progress/67890.zip → source_type=2
   - zip_anna/in_progress/11111.zip → source_type=3
3. Извлечь source_id из имени файла (поддержка форматов: 12345.zip, archive_12345.zip, book.12345.zip)
4. Проверить наличие в PostgreSQL book_sources по (source_type, source_id)
5. Если обработан → переместить в /processed/XXXYYY/ (группировка по тысячам)
6. Если не обработан → вернуть в /source/ (для повторного сканирования)
```

**Ключевое улучшение:** Определение source_type по структуре каталогов исключает риск неверной атрибуции файлов между источниками, что могло приводить к потере данных.

**Результат:** Потерянные файлы надежно возвращаются в правильный источник для обработки.

### 3️⃣ Очистка поврежденных записей Redis

**Проблема:** Поврежденные JSON записи в очередях Redis.

**Алгоритм:**
```
1. Проверить парсинг JSON всех записей в books:queue:processing
2. Удалить записи с ошибками декодирования
3. Логировать количество очищенных записей
```

**Результат:** Очереди содержат только валидные задачи.

### 4️⃣ Проверка целостности данных

**Проблема:** Сироты в базе данных (записи без связей).

**Алгоритм:**
```sql
-- Источники без книг
SELECT COUNT(*) FROM book_sources bs 
LEFT JOIN books b ON bs.book_id = b.id 
WHERE b.id IS NULL

-- Авторы без книг  
SELECT COUNT(*) FROM authors a 
LEFT JOIN book_authors ba ON a.id = ba.author_id 
WHERE ba.author_id IS NULL
```

**Результат:** Отчет о целостности данных с предупреждениями.

## 🛠️ Команды и режимы работы

### Основные команды

```bash
# Полное аварийное восстановление (рекомендуется)
python run_00_recovery.py

# Непрерывный мониторинг (для серверов)
python run_00_recovery.py --monitor

# Проверка справки
python run_00_recovery.py --help
```

### Режимы работы

| Режим | Команда | Описание | Когда использовать |
|-------|---------|----------|-------------------|
| **Полное восстановление** | `python run_00_recovery.py` | Выполняет все 4 типа восстановления | После сбоев, перед важными операциями |
| **Непрерывный мониторинг** | `python run_00_recovery.py --monitor` | Проверяет систему каждые 5 минут | На продакшн серверах |

## 📊 Отчет о восстановлении

### Структура отчета

```
🏥 ОТЧЕТ О ВОССТАНОВЛЕНИИ СИСТЕМЫ
==================================================

1️⃣ ЗАВИСШИЕ ЗАДАЧИ:
   - Восстановлено: 3
   - В карантин: 1
   - Ошибок: 0

2️⃣ ПОТЕРЯННЫЕ ФАЙЛЫ:
   - Найдено: 5
   - Восстановлено: 4
   - Ошибок: 1

3️⃣ ОЧИСТКА REDIS:
   - Поврежденных задач: 2
   - Очищено: 2
   - Ошибок: 0

4️⃣ ЦЕЛОСТНОСТЬ ДАННЫХ:
   - Всего книг в БД: 15420
   - Источников-сирот: 0
   - Авторов без книг: 3
   - Ошибок: 0

📊 ТЕКУЩЕЕ СОСТОЯНИЕ ОЧЕРЕДЕЙ:
   - Новых задач: 12
   - В обработке: 0
   - Завершено успешно: 8534
   - Кэш обработанных: 0
   - Файлов в обработке: 0
   - Файлов обработано: 15420
   - Файлов в карантине: 25

✅ ВОССТАНОВЛЕНИЕ ЗАВЕРШЕНО
```

### Интерпретация результатов

#### 🟢 Нормальное состояние
```
- Восстановлено: 0
- В карантин: 0  
- Ошибок: 0
- В обработке: 0-5 (воркер активен)
```

#### 🟡 Требует внимания
```
- Источников-сирот: >0 (возможны проблемы с базой)
- Файлов в карантине: >50 (много проблемных файлов)
- В обработке: >10 (возможны зависшие задачи)
```

#### 🔴 Критическое состояние
```
- Восстановлено: >10 (системные сбои)
- Ошибок: >0 (проблемы с файловой системой/БД)
- Потерянных файлов: >5 (сбои воркера)
```

## 🎯 Сценарии использования

### После сбоев системы

**Когда:** После аварийного завершения, зависания воркера, перезагрузки сервера.

```bash
# 1. Остановите все воркеры
pkill -f run_20_process_book_worker.py

# 2. Запустите восстановление
python run_00_recovery.py

# 3. Анализируйте отчет
# Если ошибок нет - перезапускайте воркеры
python run_20_process_book_worker.py &

# 4. Проверьте что система работает
python run_10_scan_sources.py --quiet
```

### Профилактическое обслуживание

**Когда:** Еженедельно, перед важными операциями, при подозрениях на проблемы.

```bash
# Добавьте в cron (каждое воскресенье в 03:00)
0 3 * * 0 cd /path/to/books && python run_00_recovery.py >> /var/log/books/recovery.log 2>&1
```

### Непрерывный мониторинг

**Когда:** На продакшн серверах с высокой нагрузкой.

```bash
# Запуск в фоне (с автозапуском через systemd)
python run_00_recovery.py --monitor &

# Или через screen/tmux
screen -S recovery -d -m python run_00_recovery.py --monitor
```

## 🔍 Диагностика проблем

### Зависшие задачи (processing очередь)

**Симптомы:**
```bash
redis-cli LLEN books:queue:processing
# Результат: >0 при отсутствии воркеров >5 минут
```

**Причины:**
- Аварийное завершение воркера
- Нехватка места на диске
- Ошибки PostgreSQL подключения
- Поврежденные архивы

**Решение:**
```bash
python run_00_recovery.py
# Автоматически восстановит зависшие задачи
```

### Потерянные файлы (in_progress)

**Симптомы:**
```bash
find /path/to/sources/*/in_progress/ -name "*.zip" | wc -l
# Результат: >0 при отсутствии воркеров
```

**Причины:**
- Сбой во время обработки файла
- Ошибки файловой системы
- Некорректное завершение воркера

**Решение:**
```bash
python run_00_recovery.py
# Автоматически вернет файлы в обработку или архив
```

### Поврежденные записи Redis

**Симптомы:**
```bash
# Ошибки в логах воркера
❌ Ошибка декодирования задачи: json.JSONDecodeError
```

**Причины:**
- Сбой Redis во время записи
- Некорректное завершение процессов
- Ошибки сети

**Решение:**
```bash
python run_00_recovery.py
# Автоматически очистит поврежденные записи
```

### Сироты в базе данных

**Симптомы:**
```
⚠️ Найдено X источников без книг
⚠️ Найдено Y авторов без книг
```

**Причины:**
- Прерванные транзакции
- Ошибки в логике сохранения
- Ручные операции с БД

**Решение:**
```sql
-- Проверка источников-сирот
SELECT bs.*, b.id as book_exists 
FROM book_sources bs 
LEFT JOIN books b ON bs.book_id = b.id 
WHERE b.id IS NULL 
LIMIT 10;

-- Проверка авторов без книг
SELECT a.*, ba.author_id as has_books
FROM authors a 
LEFT JOIN book_authors ba ON a.id = ba.author_id 
WHERE ba.book_id IS NULL 
LIMIT 10;

-- Очистка источников-сирот (осторожно!)
DELETE FROM book_sources WHERE book_id NOT IN (SELECT id FROM books);
```

## 🚀 Автоматизация (Cron)

### Еженедельное восстановление

```bash
# Каждое воскресенье в 03:00 (низкая нагрузка)
0 3 * * 0 cd /path/to/books && python run_00_recovery.py >> /var/log/books/recovery.log 2>&1
```

### Ежедневная проверка

```bash
# Каждый день в 01:00 - быстрая проверка
0 1 * * * cd /path/to/books && timeout 300 python run_00_recovery.py --monitor || true
```

### После перезагрузки сервера

```bash
# В systemd unit для автозапуска
ExecStartPost=/usr/bin/python /path/to/books/run_00_recovery.py
```

## 📈 Мониторинг и алерты

### Ключевые метрики для мониторинга

```bash
# Размеры очередей (должны быстро обнуляться)
redis-cli LLEN books:queue:new
redis-cli LLEN books:queue:processing

# Количество файлов в обработке (должно быть 0-5)
find /path/to/sources/*/in_progress/ -name "*.zip" | wc -l

# Количество файлов в карантине (рост = проблемы)
find /path/to/sources/*/quarantine/ -name "*.zip" | wc -l

# Статистика БД
psql $DATABASE_URL -c "SELECT COUNT(*) FROM book_sources;"
```

### Настройка алертов

```bash
# Скрипт для мониторинга (добавить в cron каждые 15 минут)
#!/bin/bash
PROCESSING=$(redis-cli LLEN books:queue:processing)
if [ $PROCESSING -gt 10 ]; then
    echo "ALERT: Too many tasks in processing queue: $PROCESSING" | mail -s "Books Processing Alert" <EMAIL>
fi

IN_PROGRESS=$(find /path/to/sources/*/in_progress/ -name "*.zip" | wc -l)
if [ $IN_PROGRESS -gt 5 ]; then
    echo "ALERT: Too many files in progress: $IN_PROGRESS" | mail -s "Books Files Alert" <EMAIL>
fi
```

## 🎯 Рекомендации по использованию

### Оптимальная стратегия

1. **Ежедневно:** Быстрая проверка через `--monitor` режим
2. **Еженедельно:** Полное восстановление `python run_00_recovery.py`
3. **После сбоев:** Немедленное восстановление перед перезапуском
4. **Перед важными операциями:** Профилактическое восстановление

### Интеграция с системой

```bash
# 1. Остановка всех процессов
systemctl stop books-worker
systemctl stop books-scanner

# 2. Восстановление
python run_00_recovery.py

# 3. Проверка результата (должно быть 0 ошибок)
if [ $? -eq 0 ]; then
    systemctl start books-worker
    systemctl start books-scanner
else
    echo "Recovery failed, manual intervention required"
fi
```

### Потребление ресурсов

- **CPU:** Низкое (I/O bound операции)
- **RAM:** ~50-100 MB
- **Диск:** Только чтение/перемещение файлов
- **Сеть:** Минимальное (локальные БД/Redis)
- **Время выполнения:** 10-60 секунд в зависимости от количества проблем

---

**Важно:** Recovery система безопасна - она НЕ удаляет обработанные данные, только восстанавливает состояние очередей и файловой системы. Все операции логируются для аудита. 