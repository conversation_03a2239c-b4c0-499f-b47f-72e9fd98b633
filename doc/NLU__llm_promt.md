# Комплексный NLU-pipeline
NLP/NLU (Natural Language Understanding)
Глубокий NLP-анализ или "структурированный литературный анализ" SLA "Structured Literature Analysis"
---
Промт получения NLU-pipeline для каждой главы
{
  "role": "system",
  
  // V1
  "content": "You are a world-class literary analyst, SEO expert, and data architect. Your mission is to perform a deep, multi-faceted analysis of a book chapter. All analysis and output, including all keys and values in the JSON, must be in Russian. Your output must be a single, precise JSON object, strictly adhering to the provided schema. First, think step-by-step to deconstruct the chapter's content, characters, plot, and themes inside a <reasoning> block. Then, based on your reasoning, generate the final JSON output inside a <json> block. Do not include any other text or explanations outside these blocks."

  // V2 - для правильного вывода только json в моделях gemini-2.5-flash
  "content": "You are a world-class literary analyst and data architect. Your mission is to perform a deep, multi-faceted analysis of a book chapter. You must think step-by-step to ensure accuracy and depth, but your final output must be ONLY the JSON object.
  **Crucial Output Rules:**
  1.  The entire response MUST be a single, valid JSON object.
  2.  The response MUST start with `{` and end with `}`. No other text, explanations, or markdown formatting are allowed.
  3.  All analysis and output, including all keys and values in the JSON, MUST be in Russian."
}
---
{
  "role": "user",
  "content": "Analyze the following chapter.

**RULES:**
1.  Extract information ONLY from the provided text. Do not invent.
2.  Avoid major spoilers in descriptive fields like summaries.
3.  Focus on elements unique or significant to THIS chapter.
4.  Be mindful of the work's cultural and historical context.

**Crucial instruction: The entire JSON response, including all keys and all string values, MUST be in Russian.**

**Analyze the following chapter:**
---
[ТЕКСТ ГЛАВЫ]
---

**JSON OUTPUT SCHEMA:**

{
  "metadata": {
    "chapter_number": "number // Номер главы",
    "chapter_title": "string // Название главы, если есть"
  },
  "summary": {
    "chapter_brief": "string // Краткое изложение ключевых событий главы (3-5 предложений).",
    "chapter_type": "string // Фактический тип главы. ENUM: ['Action-heavy', 'Dialogue-driven', 'Character study', 'Political maneuvering', 'Exposition', 'Flashback']",
    "plot_impact": "string // Важность для основного сюжета книги. ENUM: ['Critical', 'High', 'Medium', 'Low', 'Subplot only']"
  },
  "entities": {
    "characters": {
      "mentioned": ["string // Список канонических имен всех просто упомянутых персонажей."],
      "active": [
        {
          "canonical_name": "string // Полное каноническое имя.",
          "aliases_in_chapter": ["string // Имена и обращения, использованные в этой главе."],
          "is_first_appearance": "boolean // Первое ли это появление персонажа в книге.",
          "role_in_chapter": "string // Краткое описание действий и влияния на события главы.",
          "development_arc": "string // Описание изменений в характере, мыслях, состоянии. Если изменений нет, указать 'стабилен'."
        }
      ]
    },
    "locations": [
      {
        "name": "string // Название локации.",
        "type": "string // Тип (город, комната, природа).",
        "significance": "string // Значимость для сюжета или атмосферы."
      }
    ],
    "other_entities": {
      "organizations": ["string"],
      "historical_events": ["string"],
      "cultural_references": ["string // Упомянутые книги, картины, музыка и т.д."]
    }
  },
  "plot_and_connections": {
    "key_events": ["string // Список ключевых событий, двигающих сюжет."],
    "conflicts": ["string // Описание основных конфликтов (внутренних и внешних), проявившихся в главе."],
    "relationship_dynamics": [
      {
        "participants": ["string", "string"],
        "change_description": "string // Как их отношения проявились или изменились в этой главе (например, 'Укрепление дружбы через совместное испытание', 'Начало конфликта из-за недопонимания')."
      }
    ],
    "foreshadowing_and_callbacks": {
      "foreshadowing": ["string // Намеки на будущие события."],
      "callbacks": ["string // Отсылки к прошлым событиям."]
    }
  },
  "style_and_atmosphere": {
    "narrative_perspective": "string // От чьего лица ведется повествование (1-е, 3-е лицо ограниченное, всезнающий автор).",
    "tone_and_mood": "string // Описание тональности (авторской) и настроения (читательского).",
    "literary_devices": ["string // Замеченные литературные приемы (метафоры, символизм, ирония)."]
  },
  "reader_value": {
    "emotional_hooks": ["string // Эмоциональные моменты, которые могут зацепить читателя."],
    "moral_dilemmas": ["string // Моральные вопросы, поднятые в главе."],
    "memorable_quotes": ["string // 2-3 самые яркие и запоминающиеся цитаты из главы."]
  }
}
"
}