# NLU-pipeline
Пример json NLU-pipeline ВСЕЙ книги поглавно
{
  "book_metadata": {
    "book_title": "Преступление и наказание",
    "book_author": "Фёдор Достоевский",
    "book_id": "978-5-389-06789-9"
  },
  "analysis_metadata": {
    "analysis_timestamp": "2025-05-21T12:30",
    "llm_model_used": "gemini-2.0-flash",
    "prompt_version": "3.2"
  },
  "chapters": [
    {
      // <--- НАЧАЛО ВЫВОДА LLM ДЛЯ ГЛАВЫ 1
      "metadata": {
        "chapter_number": 1,
        "chapter_title": "Часть первая, глава I"
      },
      "summary": {
        "chapter_brief": "В начале июля, в чрезвычайно жаркое время, под вечер, один молодой человек вышел из своей каморки...",
        "plot_function": "Exposition",
        "plot_impact": "Medium"
      },
      "entities": {
        "characters": {
          "active": [
            {
              "canonical_name": "Родион Романович Раскольников",
              "aliases_in_chapter": ["молодой человек"],
              "is_first_appearance": true,
              "role_in_chapter": "Выходит из дома, направляется к старухе-процентщице, испытывает отвращение к своему замыслу.",
              "development_arc": "Демонстрирует внутренний конфликт между гордостью и нищетой, отвращение к своему плану."
            }
          ]
        }
      },
      ... // Остальные поля из вывода LLM для главы 1
      // КОНЕЦ ВЫВОДА LLM ДЛЯ ГЛАВЫ 1 --->
    },
    {
      // <--- НАЧАЛО ВЫВОДА LLM ДЛЯ ГЛАВЫ 2
      "metadata": {
        "chapter_number": 2,
        "chapter_title": "Часть первая, глава II"
      },
      "summary": { ... },
      "entities": { ... },
      ... // Остальные поля из вывода LLM для главы 2
      // КОНЕЦ ВЫВОДА LLM ДЛЯ ГЛАВЫ 2 --->
    }
    // ... и так далее для всех глав
  ]
}