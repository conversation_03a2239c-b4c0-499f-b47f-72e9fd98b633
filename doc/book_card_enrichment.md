| Поле из JSON-анализа | Фича на сайте |
| :--- | :--- |
| `summary.chapter_brief` | Краткое содержание каждой главы (спойлеры можно скрыть). |
| `summary.chapter_type` | Теги для главы: "политические интриги", "битва", "романтическая сцена". |
| `summary.plot_impact` | Визуальный индикатор важности главы для сюжета (например, 1-5 звезд). |
| `entities.characters.active` | Список главных действующих лиц главы с описанием их роли. |
| `entities.locations` | Интерактивная карта или таймлайн перемещений героев. |
| `plot_and_connections.key_events` | Хронология ключевых событий книги. |
| `plot_and_connections.conflicts` | Фильтры по типу конфликта: "человек против человека", "внутренний конфликт". |
| `style_and_atmosphere.tone_and_mood` | **Самые ценные теги для поиска:** "напряженный", "мрачный", "юмористический". |
| `reader_value.emotional_hooks` | Облако тегов "эмоционального отклика": "шок", "сочувствие", "гнев". |
| `reader_value.moral_dilemmas` | Раздел "Над чем подумать" на странице книги. |
