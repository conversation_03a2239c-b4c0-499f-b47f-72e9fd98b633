# Этап 2: RAG Pipeline — подробный архитектурный план

> Версия: 0.1 • Дата: {{DATE}}

---

## 1. Текущее положение проекта

- **Этап 1 (Ingestion Pipeline) — завершён.**  См. `CHANGELOG.md` и `passport.md`.
- **Переход к Этапу 2 (RAG Pipeline)**: требуется построить подсистему анализа текста и семантического поиска.

## 2. Цели этапа

1. Автоматический чанкинг текста книг.  
2. Генерация эмбеддингов и сохранение их в Qdrant.  
3. Извлечение сущностей / тэгов с LLM.  
4. API для семантического поиска и анализа.

## 3. Обзор архитектуры

```mermaid
flowchart TD
    A[PostgreSQL: books] -->|новая книга| B(Redis: rag:queue:new)
    B --> C(BookChunker)
    C -->|chunks| D(Embedder GPU)
    D -->|vectors| E[Qdrant]
    C -->|raw chunks| F(LLM Tagger)
    F --> G[PostgreSQL: book_entities\nbook_tags]
    E --> H(API Search Service)
```

Компоненты:
- **BookChunker** — микросервис на FastAPI + LangChain.
- **Embedder** — gRPC служба (Triton) c GPU.
- **LLM Tagger** — асинхронный воркер для NER/тегов.
- **Qdrant Cluster** — хранение векторов.
- **Search API** — REST/gRPC слой для клиента (будет частью Этапа 3).

## 4. Дорожная карта (спринты)

| № | Подэтап | Основные действия | Ответственные | Ресурсы | Критерии готовности | Ключевые риски + mitigation |
|---|---------|------------------|---------------|---------|---------------------|-----------------------------|
| 2.0 | Kick-off (1 нед.) | Финализация ТЗ, планирование спринтов, freeze scope | PO, Tech-Lead, Scrum-Master | Confluence, Jira | Итеративный backlog + OKR | Рассинхрон ожиданий → строгий scope-freeze |
| 2.1 | DevOps & HW (1–2 нед.) | Кластер Qdrant, GPU-нод, CI/CD pipeline | DevOps Eng. | 64 GB RAM, A100 GPU, NVMe 2 TB | helm-release `qdrant` проходит `GET /ready` | Нехватка GPU → fallback runpod.io |
| 2.2 | Схема данных (1 нед.) | Коллекция `books_chunks` (dim 1536) + payload индекс | DBA, ML Eng. | Qdrant CLI | `collection_info` status green | schema-drift → миграции с checksum |
| 2.3 | BookChunker (2–3 нед.) | LangChain-чанкер, адаптивный split (≤ 500 ток.) | Python Eng.×2 | CPU node 4 vCPU | 10 книг/мин, 85 % coverage | длинные фразы → мягкий split |
| 2.4 | Embedder + очередь (3 нед.) | gRPC Embedder, батчи 128, запись в Qdrant | ML Eng., Backend Eng. | GPU node, LMDB cache | ≥ 4k emb/s, P95 < 250 ms | GPU-OOM → half-precision |
| 2.5 | LLM Tagger (2 нед.) | Extraction, tagging, сохранение в БД | NLP Eng. | mixtral-8x7b-instr (8-bit) | BLEU ≥ 0.85 | галлюцинации → pydantic валидация |
| 2.6 | Интеграция (1 нед.) | Hook в `BookProcessor`, метрики Prometheus | Core Backend | Redis, Grafana | поток ZIP→Qdrant без ручных правок | Redis back-pressure → лимит 5k |
| 2.7 | QA & Приёмка (1 нед.) | Нагрузочные, функциональные, chaos-tests | QA Team | k6, pytest | 0 сирот данных, MRR ≥ 0.7 | узкое место Qdrant I/O → scaling shards |

## 5. Инфраструктура и DevOps

- **CI/CD**: GitLab CI (`rag-pipeline`), stages: `lint → test → build → push → deploy`.
- **Observability**: Prometheus + Grafana; алерты на задержку `rag:queue` и `embeddings/sec`.
- **SRE Run-book**: восстановление ноды Qdrant ≤ 10 мин.

## 6. Схема Qdrant

```json
{
  "collection": "books_chunks",
  "vectors": { "size": 1536, "distance": "Cosine" },
  "payload_schema": {
    "book_id": "int",
    "chunk_id": "int",
    "position": "int",
    "lang": "keyword"
  }
}
```

Индексы:
- `book_id` — фильтрация по книге.
- Композитный `book_id+position` — проверка уникальности.

## 7. Интеграция с Ingestion Pipeline

1. `BookProcessor` после успешного сохранения в PostgreSQL ➜ пушит `{"book_id":..., "file_path":...}` в `rag:queue:new`.
2. Отдельный сервис `rag-worker` потребляет, триггерит чанкер/эмбеддер.
3. Метрики: `books_processed_total`, `chunks_embedded_total`, время end-to-end.

## 8. Метрики и мониторинг

| Метрика | Цель | Алерт |
|---------|------|-------|
| `rag_queue_lag_seconds` | < 120 с | Warning 120 с / Critical 300 с |
| `embeddings_throughput` | ≥ 4k emb/s | Critical < 2k |
| `qdrant_disk_util` | < 80 % | Warning 80 % / Critical 90 % |
| `llm_error_rate` | < 2 % | Critical > 5 % |

## 9. Ссылки на регламентирующие документы

- **Roadmap → "Будущие этапы / RAG Pipeline"** — `doc/roadmap.md` строки 135-142.  
- **Passport → "Подсистема анализа и обогащения контента"** — `doc/passport.md` строки 150-167.  
- **Parsing Architecture → "Семантические элементы для RAG"** — `doc/architecture/parsing_architecture.md` строки 50-60.

---

> **Следующий контрольный пункт:** ревью инфраструктурного прототипа (коллекция + CI) через 14 дней после Kick-off. 