# --- File: ./.env ---
PROJECT_NAME=books

# --- Postgres Configuration ---
POSTGRES_HOST=db
POSTGRES_PORT=65432
POSTGRES_DB=books
POSTGRES_USER=books
POSTGRES_PASSWORD=books_postgres_super_secret_password
POSTGRES_DATA_PATH=/mnt/d/Project/books/db

# --- Redis Configuration ---
REDIS_URL=redis://localhost:6379/0
WORKER_TIMEOUT=300

# Source Directories
# Пути через запятую, без пробелов
SOURCE_DIRS=/path/to/zip_flibusta,/path/to/zip_searchfloor,/path/to/zip_anna
DUBLICATES_STORAGE_PATH=/path/to/duplicates
CANONICAL_STORAGE_PATH=/path/to/canonical
CHUNKS_STORAGE_PATH=/path/to/chunks
NER_STORAGE_PATH=/path/to/ner