"""Общие утилиты для проекта обработки книг.
Содержит переиспользуемые функции для устранения дублирования кода.
"""

import re
from pathlib import Path
from typing import Optional


def get_thousand_folder(source_id: int) -> str:
    """Вычисляет папку для группировки по тысячным значениям ID.

    Используется для организации структуры папок в /processed/:
    - 826222 → "826000"
    - 828931 → "828000"
    - 1234 → "1000"
    - 999 → "0"
    - 50 → "0"

    Args:
        source_id: Числовой ID файла

    Returns:
        Имя папки (строка)

    """
    # Округляем вниз до тысяч
    thousand_value = (source_id // 1000) * 1000
    return str(thousand_value)


def get_source_dir_by_type(source_type: int) -> Optional[Path]:
    """Находит исходную директорию по типу источника.

    Args:
        source_type: Числовой тип источника

    Returns:
        Path к исходной директории или None если не найдена

    """
    from app import settings

    # Обратное сопоставление type -> name
    type_to_name = {v: k for k, v in settings.SOURCE_TYPE_MAP.items()}
    source_name = type_to_name.get(source_type)

    if not source_name:
        return None

    # Ищем директорию с соответствующим именем
    for source_dir in settings.SOURCE_DIRS:
        if source_dir.name == source_name:
            return source_dir

    return None


def extract_source_id(file_path: Path) -> Optional[int]:
    """Извлекает source_id из имени файла (последние цифры перед расширением .zip/.fb2/.epub/.txt).

    Args:
        file_path: Путь к файлу

    Returns:
        ID как целое число или None если не найден

    Examples:
        book_12345.zip → 12345
        book-v2.12345.fb2 → 12345
        archive-new-2024.epub → 2024
        flibusta.123456.txt → 123456

    """
    # Паттерн ищет последние цифры перед поддерживаемыми расширениями (zip, fb2, epub, txt)
    match = re.search(r"(?:.*\.)?(\d+)\.(?:zip|fb2|epub|txt)$", file_path.name, re.IGNORECASE)
    if match:
        return int(match.group(1))
    return None


def extract_source_info_from_filename(filename: str) -> Optional[dict[str, int]]:
    """Извлекает информацию о типе источника и ID из имени файла.

    Поддерживаемые форматы:
    - 12345.zip (автоопределение по настройкам)
    - flibusta_12345.zip
    - searchfloor_12345.zip
    - anna_12345.zip

    Args:
        filename: Имя файла

    Returns:
        Словарь с source_type и source_id или None если не удалось распознать

    """
    from app import settings

    # Убираем расширение
    base_name = filename.split(".")[0]

    # Проверяем именованные форматы (flibusta_12345, etc.)
    for source_name, source_type in settings.SOURCE_TYPE_MAP.items():
        pattern = rf"^{source_name}_(\d+)$"
        match = re.match(pattern, base_name)
        if match:
            return {"source_type": source_type, "source_id": int(match.group(1))}

    # Проверяем простой числовой формат (12345.zip)
    if base_name.isdigit():
        # Используем первый доступный source_type как значение по умолчанию
        # Это не идеально, но подходит для recovery скрипта
        if settings.SOURCE_TYPE_MAP:
            default_source_type = list(settings.SOURCE_TYPE_MAP.values())[0]
            return {"source_type": default_source_type, "source_id": int(base_name)}

    return None


def extract_source_info_from_path(file_path: Path) -> Optional[dict[str, int]]:
    """УНИВЕРСАЛЬНАЯ ФУНКЦИЯ: Извлекает информацию о источнике из пути файла.

    Определяет source_type по родительской директории (например zip_flibusta)
    и source_id из имени файла. Устраняет дублирование между воркером и recovery.

    Args:
        file_path: Полный путь к файлу

    Returns:
        Словарь с source_type и source_id или None если не удалось определить

    """
    from app import settings

    try:
        # Извлекаем source_id из имени файла
        source_id = _extract_source_id_from_filename(file_path.name)
        if not source_id:
            return None

        # Определяем source_type по структуре каталогов
        # Ищем родительскую директорию, которая соответствует одному из SOURCE_DIRS
        current_path = file_path.parent

        # Поднимаемся по иерархии до тех пор, пока не найдем соответствие с SOURCE_DIRS
        while current_path != current_path.parent:  # Пока не дошли до корня
            # Проверяем, является ли текущий путь или его родитель одним из SOURCE_DIRS
            for source_dir in settings.SOURCE_DIRS:
                if current_path == source_dir or current_path.parent == source_dir:
                    # Определяем source_name по имени директории
                    source_name = source_dir.name
                    source_type = settings.SOURCE_TYPE_MAP.get(source_name)

                    if source_type:
                        return {"source_type": source_type, "source_id": source_id}

            current_path = current_path.parent

        return None

    except Exception:
        return None


def _extract_source_id_from_filename(filename: str) -> Optional[int]:
    """Внутренняя функция: извлекает source_id из имени файла.

    Поддерживает форматы:
    - 12345.zip → 12345
    - flibusta_12345.zip → 12345
    - book_12345.zip → 12345
    """
    # Убираем расширение
    base_name = filename.split(".")[0]

    # Ищем последние цифры в имени файла (после _ или .)
    # Поддерживаемые форматы: flibusta_12345, book-v2.22222, archive.33333, 12345
    match = re.search(r"[._](\d+)$|^(\d+)$", base_name)
    if match:
        # Возвращаем найденную группу (одна из них всегда будет None)
        return int(match.group(1) or match.group(2))

    return None


def normalize_for_hash(text: str) -> str:
    """Централизованная функция нормализации текста для хэширования.

    Выполняет серьезную очистку для максимально надежной дедупликации:
    - Приведение к нижнему регистру
    - Замена ё на е для русского текста
    - Удаление всех знаков препинания и спецсимволов
    - Нормализация пробелов

    Args:
        text: Исходный текст для нормализации

    Returns:
        Нормализованная строка для хэширования
    """
    if not text:
        return ""

    # Приводим к нижнему регистру
    normalized = text.lower()

    # Заменяем ё на е для унификации русского текста
    normalized = normalized.replace("ё", "е")

    # Удаляем все знаки препинания, спецсимволы и цифры, оставляем только буквы и пробелы
    normalized = re.sub(r"[^\w\s]", " ", normalized)

    # Нормализуем пробелы - заменяем множественные пробелы на один
    normalized = re.sub(r"\s+", " ", normalized)

    # Убираем пробелы в начале и конце
    return normalized.strip()
