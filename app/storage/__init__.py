# app/storage/__init__.py

"""Модуль абстракции работы с хранилищем для сканера-инвентаризатора.

Обеспечивает единый интерфейс для работы с различными типами хранилищ:
- Локальная файловая система
- S3 и S3-совместимые хранилища

Позволяет сканеру работать независимо от типа хранилища.
"""

from .base import ArchiveMetadata, StorageManager
from .local import LocalStorageManager
from .s3 import S3StorageManager

__all__ = [
    "StorageManager",
    "ArchiveMetadata",
    "LocalStorageManager",
    "S3StorageManager",
]
