# app/storage/archive_utils.py

"""Утилиты для работы с ZIP-архивами в потоковом режиме.

Этот модуль инкапсулирует общую логику чтения файлов из ZIP-архивов,
устраняя дублирование кода между LocalStorageManager и S3StorageManager.
"""

import io
import logging
import zipfile
from pathlib import Path

from .base import StorageAccessError, StorageCorruptionError

logger = logging.getLogger(__name__)


def read_from_zip_stream(zip_stream: io.BytesIO, file_in_archive: str) -> io.BytesIO:
    """Читает конкретный файл из ZIP-потока и возвращает его содержимое как поток байт.

    Эта функция является ключевой для устранения дублирования кода между
    LocalStorageManager и S3StorageManager. Оба менеджера получают ZIP-архив
    разными способами (локальный файл vs S3), но логика извлечения файла
    из архива одинакова.

    Args:
        zip_stream: Поток байт ZIP-архива (io.BytesIO)
        file_in_archive: Имя файла внутри архива для извлечения

    Returns:
        io.BytesIO: Поток байт содержимого файла

    Raises:
        StorageCorruptionError: При поврежденном ZIP-архиве
        StorageAccessError: Если файл не найден в архиве

    Example:
        # Для LocalStorageManager:
        with open(archive_path, 'rb') as f:
            zip_stream = io.BytesIO(f.read())
        file_content = read_from_zip_stream(zip_stream, "book.fb2")

        # Для S3StorageManager:
        response = s3_client.get_object(Bucket=bucket, Key=key)
        zip_stream = io.BytesIO(response["Body"].read())
        file_content = read_from_zip_stream(zip_stream, "book.fb2")
    """
    try:
        # Сбрасываем позицию потока на начало
        zip_stream.seek(0)

        with zipfile.ZipFile(zip_stream, "r") as zip_file:
            # Проверяем, что файл существует в архиве
            if file_in_archive not in zip_file.namelist():
                available_files = zip_file.namelist()
                logger.error(f"Файл '{file_in_archive}' не найден в архиве. Доступные файлы: {available_files}")
                raise StorageAccessError(
                    f"Файл '{file_in_archive}' не найден в архиве", storage_type="archive", path=file_in_archive
                )

            # Читаем файл из архива
            with zip_file.open(file_in_archive) as file_in_zip:
                file_content = file_in_zip.read()

            logger.debug(f"Успешно извлечен файл '{file_in_archive}' из архива ({len(file_content)} байт)")

            # Возвращаем содержимое как поток байт
            return io.BytesIO(file_content)

    except zipfile.BadZipFile as e:
        logger.error(f"Поврежденный ZIP-архив: {e}")
        raise StorageCorruptionError(
            f"Поврежденный ZIP-архив: {e}", storage_type="archive", path=file_in_archive
        ) from e

    except Exception as e:
        # Если это уже наше исключение, пробрасываем дальше
        if isinstance(e, (StorageCorruptionError, StorageAccessError)):
            raise

        logger.error(f"Ошибка при чтении файла '{file_in_archive}' из ZIP-архива: {e}")
        raise StorageAccessError(
            f"Ошибка при чтении файла из архива: {e}", storage_type="archive", path=file_in_archive
        ) from e


def list_files_in_zip_stream(zip_stream: io.BytesIO) -> list[str]:
    """Возвращает список всех файлов в ZIP-архиве из потока.

    Вспомогательная функция для отладки и валидации архивов.

    Args:
        zip_stream: Поток байт ZIP-архива

    Returns:
        list[str]: Список имен файлов в архиве

    Raises:
        StorageCorruptionError: При поврежденном ZIP-архиве
    """
    try:
        zip_stream.seek(0)

        with zipfile.ZipFile(zip_stream, "r") as zip_file:
            return zip_file.namelist()

    except zipfile.BadZipFile as e:
        logger.error(f"Поврежденный ZIP-архив при получении списка файлов: {e}")
        raise StorageCorruptionError(f"Поврежденный ZIP-архив: {e}", storage_type="archive", path="<stream>") from e


def validate_zip_stream(zip_stream: io.BytesIO) -> bool:
    """Проверяет, что поток содержит валидный ZIP-архив.

    Args:
        zip_stream: Поток байт для проверки

    Returns:
        bool: True если архив валидный, False иначе
    """
    try:
        zip_stream.seek(0)

        with zipfile.ZipFile(zip_stream, "r") as zip_file:
            # Пытаемся получить список файлов - это проверит базовую структуру
            zip_file.namelist()
            return True

    except zipfile.BadZipFile:
        return False
    except Exception:
        return False


def find_book_files_in_zip_stream(zip_stream: io.BytesIO) -> list[str]:
    """Находит книжные файлы в ZIP-архиве из потока.

    Ищет файлы с расширениями .fb2, .epub, .txt, .mobi, .azw, .azw3

    Args:
        zip_stream: Поток байт ZIP-архива

    Returns:
        list[str]: Список имен книжных файлов в архиве

    Raises:
        StorageCorruptionError: При поврежденном ZIP-архиве
    """
    try:
        zip_stream.seek(0)

        book_extensions = {".fb2", ".epub", ".txt", ".mobi", ".azw", ".azw3"}
        book_files = []

        with zipfile.ZipFile(zip_stream, "r") as zip_file:
            for file_info in zip_file.infolist():
                # Игнорируем директории и системные файлы
                if file_info.is_dir() or file_info.filename.startswith("."):
                    continue

                # Проверяем расширение файла
                file_path = Path(file_info.filename)
                if file_path.suffix.lower() in book_extensions:
                    # Возвращаем только имя файла без пути
                    book_files.append(file_path.name)

        logger.debug(f"Найдено {len(book_files)} книжных файлов в архиве")
        return book_files

    except zipfile.BadZipFile as e:
        logger.error(f"Поврежденный ZIP-архив при поиске книжных файлов: {e}")
        raise StorageCorruptionError(f"Поврежденный ZIP-архив: {e}", storage_type="archive", path="<stream>") from e
