# app/processing/fragment_detector.py

import logging
import re

from .canonical_model import CanonicalBook

logger = logging.getLogger(__name__)

# Список ключевых фраз для обнаружения. Легко расширяется.
# Все фразы приводятся к нижнему регистру для регистронезависимого поиска.
FRAGMENT_MARKERS = [
    "конец ознакомительного фрагмента",
    "прочитайте эту книгу целиком",
    "текст предоставлен правообладателем",
    "конец бесплатного фрагмента",
    "купить полную версию",
    "полная версия книги",
    "ознакомительный фрагмент",
]


class FragmentDetector:
    """Компонент для обнаружения ознакомительных фрагментов книг."""

    def __init__(self):
        # Компилируем регулярное выражение для очистки текста перед проверкой
        self._clean_regex = re.compile(r"[\s\.,!?-]+")
        # Сохраняем последний найденный маркер для debug режима
        self._last_found_marker = None

    def _normalize_text(self, text: str) -> str:
        """Приводит текст к единому виду для надежного поиска."""
        # Убираем все знаки препинания и лишние пробелы, приводим к нижнему регистру
        return self._clean_regex.sub("", text.lower())

    def is_fragment(self, book: CanonicalBook) -> bool:
        """Проверяет, является ли книга фрагментом, анализируя последнюю главу.

        Args:
            book: Каноническая модель книги.

        Returns:
            True, если книга является фрагментом, иначе False.

        """
        # Сбрасываем предыдущий маркер
        self._last_found_marker = None

        if not book.chapters:
            return False

        # Анализируем только последнюю главу – это самое эффективное место для поиска.
        last_chapter = book.chapters[-1]
        content_to_check = last_chapter.content_md

        # Для надежности можно добавить и предпоследнюю, если последняя очень короткая.
        if len(content_to_check) < 300 and len(book.chapters) > 1:
            content_to_check += "\n" + book.chapters[-2].content_md

        # Нормализуем текст для поиска
        normalized_content = self._normalize_text(content_to_check)

        # Ищем любую из маркерных фраз
        for marker in FRAGMENT_MARKERS:
            normalized_marker = self._clean_regex.sub("", marker)
            if normalized_marker in normalized_content:
                # Сохраняем найденный маркер для debug режима
                self._last_found_marker = marker
                # Убираем лишний вывод: флаг сохраняем, но ничего не логируем
                return True

        return False

    def get_fragment_reason(self, book: CanonicalBook) -> str:
        """Возвращает детальную причину, почему книга считается фрагментом.

        Args:
            book: Каноническая модель книги.

        Returns:
            Строка с описанием причины
        """
        # Сначала проверяем является ли книга фрагментом
        if not self.is_fragment(book):
            return "Книга не является фрагментом"

        # Если проверка была выполнена ранее, используем сохраненный маркер
        if self._last_found_marker:
            return f"Фрагмент: найден маркер '{self._last_found_marker}'"

        return "Фрагмент: обнаружен неизвестный маркер"
