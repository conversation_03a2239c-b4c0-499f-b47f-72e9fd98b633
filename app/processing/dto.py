from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class BookSourceInfo:
    """Информация об источнике книги."""

    source_type: int
    source_id: int
    file_path: str
    processed_at: Optional[float] = None
    file_format: Optional[str] = None

    def to_metadata_dict(self) -> dict[str, Any]:
        """Возвращает метаданные источника как словарь для сериализации."""
        return {
            "file_path": self.file_path,
            "processed_at": self.processed_at,
            "file_format": self.file_format,
        }


@dataclass
class BookDTO:
    """Data Transfer Object для книги, используемый при сохранении в БД.

    Содержит все поля, необходимые для сохранения в базе данных.
    """

    # Основные обязательные поля
    title: str
    lang: str
    authors: list[dict[str, Any]]  # [{"first_name": "", "last_name": "", "middle_name": ""}]
    metadata_hash: str

    # Опциональные поля для серий
    series: Optional[str] = None
    series_number: Optional[int] = None

    # Дополнительные метаданные
    genres: Optional[list[str]] = None
    annotation: Optional[str] = None
    file_format: Optional[str] = None
    keywords: Optional[list[str]] = None
    raw_metadata: Optional[dict[str, Any]] = None

    # Информация об источнике
    source_info: Optional[BookSourceInfo] = None
