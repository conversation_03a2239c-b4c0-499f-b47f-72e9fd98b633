import logging
from enum import Enum
from typing import Any, Optional


class ErrorType(Enum):
    """Типы ошибок обработки"""

    QUARANTINE = "quarantine"  # Файл должен быть помещен в карантин
    RETRY = "retry"  # Можно попробовать обработать заново
    FATAL = "fatal"  # Критическая ошибка, требует вмешательства


class QuarantineType(Enum):
    """Типы карантина для категоризации недостойных книг"""

    TRIAL = "trial"  # Ознакомительные фрагменты
    SMALL_CONTENT = "small_content"  # Книги с недостаточным объемом текста (< MIN_CONTENT_CHARS)
    FEW_CHAPTERS = "few_chapters"  # Книги с аномально малым количеством глав (< MIN_CHAPTERS)
    ANTHOLOGIES = "anthologies"  # Сборники/рассказы от разных авторов
    ERROR = "error"  # Ошибки парсинга/технические проблемы
    FOOTNOTES = "footnotes"  # Книги с нераспарсенными сносками
    INVALID = "invalid"  # Поврежденные/нечитаемые файлы


class ProcessingError(Exception):
    """Базовое исключение для ошибок обработки"""

    def __init__(
        self,
        message: str,
        error_type: ErrorType = ErrorType.RETRY,
        details: Optional[dict[str, Any]] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.details = details or {}


class QuarantineError(ProcessingError):
    """Исключение для файлов, которые должны попасть в карантин"""

    def __init__(
        self,
        message: str,
        quarantine_type: QuarantineType = QuarantineType.ERROR,
        details: Optional[dict[str, Any]] = None,
    ):
        super().__init__(message, ErrorType.QUARANTINE, details)
        self.quarantine_type = quarantine_type


class FatalError(ProcessingError):
    """Исключение для критических ошибок системы"""

    def __init__(self, message: str, details: Optional[dict[str, Any]] = None):
        super().__init__(message, ErrorType.FATAL, details)


class ErrorHandler:
    """Централизованный обработчик ошибок для воркера.
    Принимает решения о дальнейших действиях на основе типа ошибки.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.retry_counts = {}  # Счетчик попыток для каждой задачи
        self.max_retries = 3  # Максимальное количество повторных попыток

        # Инициализируем реестр стратегий (ленивая загрузка чтобы избежать циклических импортов)
        self._strategy_registry: Any = None

    def handle_error(self, error: Exception, task_data: dict[str, Any], context: str = "") -> dict[str, Any]:
        """Обрабатывает ошибку и возвращает решение о дальнейших действиях.

        Args:
            error: Возникшее исключение
            task_data: Данные обрабатываемой задачи
            context: Контекст возникновения ошибки

        Returns:
            Словарь с решением: {'action': 'quarantine'|'retry'|'fatal', 'reason': 'текст', 'details': {...}}

        """
        file_path = task_data.get("file_path", "unknown")

        # Логируем ошибку
        self.logger.error(
            f"Ошибка обработки файла {file_path} в контексте '{context}': {error}",
            exc_info=True,
        )

        # Определяем тип ошибки и действие
        if isinstance(error, QuarantineError):
            return self._handle_quarantine_error(error, task_data)
        elif isinstance(error, FatalError):
            return self._handle_fatal_error(error, task_data)
        elif isinstance(error, ProcessingError):
            return self._handle_processing_error(error, task_data)
        else:
            # Неизвестный тип ошибки - используем стратегии
            return self._analyze_unknown_error(error, task_data, context)

    def _handle_quarantine_error(self, error: QuarantineError, task_data: dict[str, Any]) -> dict[str, Any]:
        """Обрабатывает ошибки, требующие помещения в карантин"""
        return {
            "action": "quarantine",
            "reason": error.message,
            "quarantine_type": error.quarantine_type,
            "details": error.details,
            "final": True,  # Повторная обработка не нужна
        }

    def _handle_fatal_error(self, error: FatalError, task_data: dict[str, Any]) -> dict[str, Any]:
        """Обрабатывает критические ошибки системы"""
        return {
            "action": "fatal",
            "reason": error.message,
            "details": error.details,
            "final": True,
        }

    def _handle_processing_error(self, error: ProcessingError, task_data: dict[str, Any]) -> dict[str, Any]:
        """Обрабатывает обычные ошибки обработки"""
        if error.error_type == ErrorType.QUARANTINE:
            # Создаем QuarantineError для соответствия типам
            quarantine_error = QuarantineError(error.message, details=error.details)
            return self._handle_quarantine_error(quarantine_error, task_data)
        elif error.error_type == ErrorType.FATAL:
            # Создаем FatalError для соответствия типам
            fatal_error = FatalError(error.message, error.details)
            return self._handle_fatal_error(fatal_error, task_data)
        else:  # RETRY
            return self._handle_retry_error(error, task_data)

    def _handle_retry_error(self, error: ProcessingError, task_data: dict[str, Any]) -> dict[str, Any]:
        """Обрабатывает ошибки с возможностью повтора"""
        file_path = task_data.get("file_path", "unknown")
        retry_count = self.retry_counts.get(file_path, 0)

        if retry_count < self.max_retries:
            self.retry_counts[file_path] = retry_count + 1
            return {
                "action": "retry",
                "reason": f"Повторная попытка {retry_count + 1}/{self.max_retries}: {error.message}",
                "details": error.details,
                "final": False,
            }
        else:
            # Превышено количество попыток - отправляем в карантин
            self.retry_counts.pop(file_path, None)  # Очищаем счетчик
            return {
                "action": "quarantine",
                "reason": f"Превышено максимальное количество попыток ({self.max_retries}): {error.message}",
                "details": error.details,
                "final": True,  # ВАЖНО: это финальное действие!
            }

    def _get_strategy_registry(self):
        """Получает реестр стратегий (ленивая инициализация)"""
        if self._strategy_registry is None:
            from .error_strategies import ErrorStrategyRegistry

            self._strategy_registry = ErrorStrategyRegistry()
        return self._strategy_registry

    def _analyze_unknown_error(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        """Анализирует неизвестные ошибки используя паттерн "Стратегия".

        ВАЖНО: Использует зарегистрированные стратегии для типизированной обработки ошибок:
        - Поврежденные архивы (zipfile.BadZipFile, EOFError) → обнуляются (QuarantineType.INVALID)
        - Системные ошибки (БД, диск, сеть) → перепаковываются с удалением картинок (QuarantineType.ERROR)
        - Ошибки кодировки (UnicodeError) → перепаковываются (могут быть исправлены)
        - Критические ошибки памяти (MemoryError) → требуют немедленного вмешательства
        """
        registry = self._get_strategy_registry()
        strategy = registry.find_strategy(error)

        if strategy:
            result = strategy.handle(error, task_data, context)
            # Если стратегия возвращает специальный флаг retry_error - обрабатываем через retry
            if result.get("action") == "retry_error":
                return self._handle_retry_error(result["error"], result["task_data"])
            return result

        # Если подходящая стратегия не найдена - пробуем повторить
        error_type = type(error).__name__
        return self._handle_retry_error(
            ProcessingError(f"Неизвестная ошибка: {error_type}: {error}", ErrorType.RETRY), task_data
        )

    def reset_retry_count(self, file_path: str):
        """Сбрасывает счетчик повторов для файла после успешной обработки"""
        self.retry_counts.pop(file_path, None)

    def get_retry_stats(self) -> dict[str, int]:
        """Возвращает статистику повторных попыток"""
        return {
            "files_with_retries": len(self.retry_counts),
            "total_retry_attempts": sum(self.retry_counts.values()),
        }
