# app/processing/date_extractor.py

import logging
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Union

from dateutil.parser import parse as parse_date

from app.processing.parsers.fb2.fb2_model import FB2Book

logger = logging.getLogger(__name__)


def _detect_date_format(date_str: str, lang: Optional[str] = None) -> bool:
    """Определяет, нужно ли использовать формат день-первый для парсинга даты.

    Args:
        date_str: Строка с датой
        lang: Язык книги ('ru', 'en', etc.)

    Returns:
        True если нужно использовать день-первый формат (русский), False иначе
    """
    date_clean = date_str.strip()

    # ISO формат YYYY-MM-DD всегда парсится одинаково, независимо от языка
    if re.match(r"^\d{4}-\d{1,2}-\d{1,2}$", date_clean):
        return False

    # Для русского языка используем день-первый формат для других форматов
    if lang and lang.lower() in ("ru", "rus", "russian"):
        return True

    # Для неопределенного языка пытаемся определить по формату строки
    if not lang:
        # Если строка содержит точки как разделители, скорее всего это русский формат
        if re.match(r"^\d{1,2}\.\d{1,2}\.\d{4}$", date_clean):
            # Дополнительная проверка: если первое число > 12, то это точно день
            parts = date_clean.split(".")
            if len(parts) == 3 and parts[0].isdigit() and int(parts[0]) > 12:
                return True
            # Если второе число > 12, то первое это день, второе месяц - русский формат
            if len(parts) == 3 and parts[1].isdigit() and int(parts[1]) > 12:
                return False  # Некорректная дата, но попробуем американский формат
            # В остальных случаях для формата с точками используем русский
            return True

    return False


def _parse_flexible_date(date_str: Optional[str], lang: Optional[str] = None) -> Optional[datetime]:
    """Парсит дату с учетом языка книги.

    Args:
        date_str: Строка с датой
        lang: Язык книги для определения формата даты

    Returns:
        Парсенная дата или None при ошибке
    """
    if not date_str or not date_str.strip():
        logger.debug(f"      _parse_flexible_date: пустая строка '{date_str}'")
        return None

    date_clean = date_str.strip()
    logger.debug(f"      _parse_flexible_date: парсим '{date_clean}' (язык: {lang})")

    # ИСПРАВЛЕНИЕ: Специальная обработка строк содержащих только год (4 цифры)
    if date_clean.isdigit() and len(date_clean) == 4:
        try:
            year = int(date_clean)
            # Для года устанавливаем 1 января 00:00:00 UTC
            result = datetime(year, 1, 1, tzinfo=timezone.utc)
            logger.debug(f"      _parse_flexible_date: год '{date_clean}' -> {result}")
            return result
        except ValueError as e:
            logger.debug(f"      _parse_flexible_date: ошибка парсинга года '{date_clean}': {e}")
            return None

    try:
        # Определяем формат даты на основе языка
        use_day_first = _detect_date_format(date_clean, lang)

        logger.debug(f"      _parse_flexible_date: используем day_first={use_day_first} для '{date_clean}'")

        # Парсим дату с правильным форматом
        parsed_dt = parse_date(date_clean, fuzzy=True, dayfirst=use_day_first)

        # Если время не содержит информации о часовом поясе, считаем его UTC
        if parsed_dt.tzinfo is None:
            parsed_dt = parsed_dt.replace(tzinfo=timezone.utc)

        logger.debug(f"      _parse_flexible_date: успех -> {parsed_dt}")
        return parsed_dt
    except (ValueError, TypeError, OverflowError) as e:
        logger.debug(f"      _parse_flexible_date: ошибка при парсинге '{date_clean}': {e}")
        return None


def _is_date_only_year(dt: datetime, source_str: str) -> bool:
    """Определяет, содержит ли дата только год (день и месяц по умолчанию).

    Args:
        dt: Парсенная дата
        source_str: Исходная строка для анализа

    Returns:
        True если дата содержит только год (1 января, 00:00:00)
    """
    # Проверяем что это 1 января и 00:00:00 (дефолтные значения dateutil.parser)
    if dt.month == 1 and dt.day == 1 and dt.hour == 0 and dt.minute == 0 and dt.second == 0:
        # Дополнительно проверяем что исходная строка содержит только год (4 цифры)
        source_clean = source_str.strip()
        if source_clean.isdigit() and len(source_clean) == 4:
            return True
    return False


def _try_extract_date_from_source(
    source_info, source_name: str, lang: Optional[str] = None
) -> tuple[Optional[datetime], Optional[str]]:
    """Извлекает дату из источника FB2.

    Args:
        source_info: Информация из источника FB2
        source_name: Название источника
        lang: Язык книги для правильного парсинга дат

    Returns:
        Tuple (parsed_date, source_string) или (None, None) если дата не найдена
    """
    logger.debug(
        f"    _try_extract_date_from_source({source_name}): source_info={source_info is not None}, lang={lang}"
    )

    if source_name == "title-info":
        if source_info and source_info.date:
            info = source_info.date
            logger.debug(f"    title-info: trying value='{info.value}', text='{info.text}'")
            dt = _parse_flexible_date(info.value, lang) or _parse_flexible_date(info.text, lang)
            source_str = info.value or info.text
            logger.debug(f"    title-info: результат dt={dt}, source_str='{source_str}'")
            return dt, source_str
        else:
            logger.debug(
                f"    title-info: source_info={source_info is not None}, date={source_info.date if source_info else 'N/A'}"
            )

    elif source_name == "publish-info":
        if source_info and source_info.year:
            year_str = str(source_info.year)
            logger.debug(f"    publish-info: trying year_str='{year_str}'")
            dt = _parse_flexible_date(year_str, lang)
            logger.debug(f"    publish-info: результат dt={dt}, source_str='{year_str}'")
            return dt, year_str
        else:
            logger.debug(
                f"    publish-info: source_info={source_info is not None}, year={source_info.year if source_info else 'N/A'}"
            )

    elif source_name == "document-info":
        if source_info and source_info.date:
            info = source_info.date
            logger.debug(f"    document-info: trying value='{info.value}', text='{info.text}'")
            dt = _parse_flexible_date(info.value, lang) or _parse_flexible_date(info.text, lang)
            source_str = info.value or info.text
            logger.debug(f"    document-info: результат dt={dt}, source_str='{source_str}'")
            return dt, source_str
        else:
            logger.debug(
                f"    document-info: source_info={source_info is not None}, date={source_info.date if source_info else 'N/A'}"
            )

    logger.debug(f"    {source_name}: возвращаем (None, None)")
    return None, None


def extract_best_date(fb2_book: FB2Book, file_mtime: Optional[Union[float, Path]] = None) -> tuple[datetime, str]:
    """Извлекает наиболее точную дату из СЫРОЙ FB2-модели.
    Приоритет источников: title-info -> publish-info -> document-info -> mtime файла.
    Приоритет важнее полноты даты: если источник более высокого приоритета содержит
    только год, он не заменяется более полной датой из источника ниже.

    Возвращает кортеж (datetime в UTC для согласованности с UUID v7, source_name).
    """
    # Определяем название источника для логирования
    if isinstance(file_mtime, Path):
        log_source = file_mtime.name
    else:
        log_source = "in-memory"

    logger.debug(f"=== ИЗВЛЕЧЕНИЕ ДАТЫ ({log_source}) ===")

    # Определяем язык книги для правильного парсинга дат
    book_lang = None
    if fb2_book.description and fb2_book.description.title_info and fb2_book.description.title_info.lang:
        book_lang = fb2_book.description.title_info.lang
        logger.debug(f"Определен язык книги: {book_lang}")

    # Преобразуем file_mtime к числу (timestamp) если это Path
    mtime_ts: Optional[float] = None
    if isinstance(file_mtime, Path):
        try:
            mtime_ts = file_mtime.stat().st_mtime
        except Exception:
            mtime_ts = None
    elif isinstance(file_mtime, (int, float)):
        mtime_ts = float(file_mtime)

    if not fb2_book.description:
        logger.debug("FB2 без description - используем file_mtime")
        if mtime_ts is not None:
            result = datetime.fromtimestamp(mtime_ts, tz=timezone.utc)
            logger.debug(f"Результат (file_mtime): {result}")
            return result, "file_mtime"
        else:
            now_dt = datetime.now(timezone.utc)
            logger.debug(f"Результат (fallback_now): {now_dt}")
            return now_dt, "fallback_now"

    # Диагностика содержимого FB2
    logger.debug("=== ДИАГНОСТИКА FB2 ИСТОЧНИКОВ ===")

    # title-info
    if fb2_book.description.title_info and fb2_book.description.title_info.date:
        ti_date = fb2_book.description.title_info.date
        logger.debug(f"title-info.date: value='{ti_date.value}', text='{ti_date.text}'")
    else:
        logger.debug("title-info.date: НЕТ")

    # publish-info
    if fb2_book.description.publish_info:
        pi = fb2_book.description.publish_info
        logger.debug(f"publish-info: year={pi.year}, book_name='{pi.book_name}', publisher='{pi.publisher}'")
    else:
        logger.debug("publish-info: НЕТ")

    # document-info
    if fb2_book.description.document_info and fb2_book.description.document_info.date:
        di_date = fb2_book.description.document_info.date
        logger.debug(f"document-info.date: value='{di_date.value}', text='{di_date.text}'")
    else:
        logger.debug("document-info.date: НЕТ")

    # Собираем доступные источники дат в порядке приоритета
    sources = [
        (fb2_book.description.title_info, "title-info"),
        (fb2_book.description.publish_info, "publish-info"),
        (fb2_book.description.document_info, "document-info"),
    ]

    found_date = None
    found_source = None

    logger.debug("=== ПОИСК ПЕРВОЙ ДОСТУПНОЙ ДАТЫ ===")
    # Ищем первую доступную дату по иерархии
    for source_info, source_name in sources:
        logger.debug(f"Проверяем источник: {source_name}")
        dt, source_str = _try_extract_date_from_source(source_info, source_name, book_lang)
        if dt:
            logger.debug(
                f"✅ Найдена дата в {source_name}: {dt} из строки '{source_str}' (день-первый формат: {_detect_date_format(source_str or '', book_lang)})"
            )
            found_date = dt
            found_source = source_name
            break
        else:
            logger.debug(f"❌ Дата не найдена в {source_name}")

    if found_date:
        logger.debug(f"🎯 ИТОГОВАЯ дата извлечена из {found_source}: {found_date}")
        return found_date, found_source

    # --- Fallback: Дата модификации файла или текущий момент ---
    logger.debug("⚠️ Дата не найдена в метаданных. Используется дата модификации файла или текущее время.")
    if mtime_ts is not None:
        result = datetime.fromtimestamp(mtime_ts, tz=timezone.utc)
        logger.debug(f"Результат (file_mtime fallback): {result}")
        return result, "file_mtime"

    now_dt = datetime.now(timezone.utc)
    logger.debug(f"Результат (fallback_now): {now_dt}")
    return now_dt, "fallback_now"
