# app/processing/canonical_model.py

"""
🚨 ВАЖНО: Каноническая модель УЖЕ ПОЛНОСТЬЮ РЕАЛИЗОВАНА!

Эта модель является универсальным представлением книги для всех форматов (FB2/EPUB/MOBI).
Используется во всем пайплайне после парсинга.

НЕ ПЕРЕПИСЫВАЙ! Если нужны изменения - дорабатывай существующее.

Архитектура:
- CanonicalBook: основная модель с метаданными и главами
- CanonicalChapter: глава с заголовком и Markdown контентом
- CanonicalAuthor: информация об авторе
- CanonicalSequence: информация о серии

См. doc/ПАРСИНГ_УЖЕ_РЕАЛИЗОВАН.md для деталей.
"""

from dataclasses import dataclass, field
from datetime import date
from typing import Any, Optional

# --- Вспомогательные структуры для метаданных ---
# Эти классы остаются, так как они содержат базовую информацию об авторе.


@dataclass
class CanonicalAuthor:
    """Универсальное представление автора."""

    first_name: Optional[str]
    middle_name: Optional[str]
    last_name: Optional[str]
    nickname: Optional[str] = None


@dataclass
class CanonicalSequence:
    """Информация о серии книг."""

    name: str
    number: Optional[int] = None


# --- Ключевая структура для глав ---
# Реализует твое требование: простая сущность с заголовком и всем содержимым в виде Markdown.


@dataclass
class CanonicalChapter:
    """Каноническое представление одной главы.
    Содержит заголовок и ВСЕ содержимое главы в виде единой Markdown строки.
    """

    title: str
    content_md: str


# --- Основной канонический DTO ---
# Корневой объект, который будет сериализован в JSON.
# Содержит только согласованные нами поля.


@dataclass
class CanonicalBook:
    """Единое, универсальное представление книги, сфокусированное на текстовом
    содержимом для RAG-пайплайна. Поля упорядочены для оптимальной сериализации.
    """

    # --- Основные метаданные (в начале) ---
    title: str
    lang: str
    authors: list[CanonicalAuthor] = field(default_factory=list)
    translators: list[CanonicalAuthor] = field(default_factory=list)
    sequences: list[CanonicalSequence] = field(default_factory=list)
    publication_date: Optional[date] = None
    genres: list[str] = field(default_factory=list)
    keywords: list[str] = field(default_factory=list)

    # --- Информация об источнике ---
    source_id: Optional[int] = None
    source_type: Optional[int] = None

    # --- Дополнительные метаданные ---
    source_format: str = ""
    raw_metadata: dict[str, Any] = field(default_factory=dict)
    raw_source_model: Optional[Any] = None  # Сырая модель для date_extractor

    # --- Информация о дате для генерации book_id ---
    book_id_generation_date: Optional[str] = None  # Дата использованная для генерации book_id
    book_id_date_source: Optional[str] = None  # Источник даты (title-info, publish-info, document-info, file_mtime)

    # --- Содержимое (в конце) ---
    annotation_md: str = ""
    chapters: list[CanonicalChapter] = field(default_factory=list)
