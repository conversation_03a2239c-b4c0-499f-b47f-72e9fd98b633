# app/processing/book_data_builder.py

import logging
from typing import Any

from .canonical_model import CanonicalBook
from .dto import BookDTO, BookSourceInfo


class BookDataBuilder:
    """Строитель для создания BookDTO из канонической модели книги.
    Инкапсулирует логику извлечения и подготовки данных из CanonicalBook.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def build_book_dto_from_canonical(
        self,
        canonical_book: CanonicalBook,
        task_data: dict[str, Any],
        metadata_hash: str,
    ) -> BookDTO:
        """Создает BookDTO напрямую из CanonicalBook и данных задачи.

        Args:
            canonical_book: Каноническая модель книги
            task_data: Данные задачи из очереди
            metadata_hash: Хэш метаданных

        Returns:
            Полностью подготовленный BookDTO
        """
        # Создаем информацию об источнике из CanonicalBook
        source_info = self._build_source_info_from_canonical(task_data, canonical_book)

        # Преобразуем CanonicalAuthor объекты в dict формат для DTO
        authors_data = []
        for author in canonical_book.authors:
            authors_data.append(
                {
                    "first_name": author.first_name or "",
                    "last_name": author.last_name or "",
                    "middle_name": author.middle_name or "",
                }
            )

        # Извлекаем серию из sequences (берем первую, если есть)
        series_name = None
        series_number = None
        if canonical_book.sequences:
            first_sequence = canonical_book.sequences[0]
            series_name = first_sequence.name
            series_number = first_sequence.number

        # 1. Собираем только РЕАЛЬНО дополнительные метаданные
        extra_metadata = {}
        # Словарь, который сделал трансформер
        raw_meta = canonical_book.raw_metadata

        if raw_meta.get("document_info"):
            extra_metadata["document_info"] = raw_meta["document_info"]

        if raw_meta.get("publish_info"):
            extra_metadata["publish_info"] = raw_meta["publish_info"]

        # Добавляем keywords, если они есть
        if raw_meta.get("title_info", {}).get("keywords"):
            extra_metadata["keywords"] = raw_meta["title_info"]["keywords"]

        # 2. Создаем DTO с очищенными метаданными
        book_dto = BookDTO(
            title=canonical_book.title,
            lang=canonical_book.lang,
            authors=authors_data,
            series=series_name,
            series_number=series_number,
            genres=canonical_book.genres,
            annotation=canonical_book.annotation_md,
            file_format=canonical_book.source_format,
            keywords=canonical_book.keywords,
            raw_metadata=extra_metadata,
            metadata_hash=metadata_hash,
            source_info=source_info,
        )

        return book_dto

    def _build_source_info_from_canonical(
        self, task_data: dict[str, Any], canonical_book: CanonicalBook
    ) -> BookSourceInfo:
        """Создает BookSourceInfo из данных задачи и канонической модели"""
        return BookSourceInfo(
            source_type=task_data.get("source_type", 0),
            source_id=task_data.get("source_id", 0),
            file_path=task_data.get("file_path", ""),
            processed_at=task_data.get("_claimed_at"),
            file_format=canonical_book.source_format,
        )
