# app/processing/error_strategies.py

import zipfile
from abc import ABC, abstractmethod
from typing import Any

import psycopg

from .error_handler import ErrorType, ProcessingError, QuarantineType


class ErrorStrategy(ABC):
    """Базовый класс стратегии обработки ошибок"""

    @abstractmethod
    def can_handle(self, error: Exception) -> bool:
        """Проверяет, может ли стратегия обработать данную ошибку"""
        pass

    @abstractmethod
    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        """Обрабатывает ошибку и возвращает решение"""
        pass


class CorruptedArchiveStrategy(ErrorStrategy):
    """Стратегия для поврежденных архивов - обнуляем содержимое"""

    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, (zipfile.BadZipFile, zipfile.LargeZipFile, EOFError))

    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        return {
            "action": "quarantine",
            "reason": f"Поврежденный архив: {type(error).__name__}: {error}",
            "details": {"context": context, "error_type": type(error).__name__},
            "quarantine_type": QuarantineType.INVALID,  # Обнуляем содержимое
            "final": True,
        }


class DatabaseErrorStrategy(ErrorStrategy):
    """Стратегия для ошибок БД - используем перепаковку"""

    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, psycopg.Error)

    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        return {
            "action": "quarantine",
            "reason": f"Ошибка БД при обработке: {type(error).__name__}: {error}",
            "details": {"context": context, "error_type": type(error).__name__},
            "quarantine_type": QuarantineType.ERROR,  # Используем перепаковку
            "final": True,
        }


class SystemErrorStrategy(ErrorStrategy):
    """Стратегия для системных ошибок ввода-вывода - используем перепаковку"""

    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, (IOError, OSError, PermissionError))

    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        return {
            "action": "quarantine",
            "reason": f"Системная ошибка при обработке: {type(error).__name__}: {error}",
            "details": {"context": context, "error_type": type(error).__name__},
            "quarantine_type": QuarantineType.ERROR,  # Используем перепаковку
            "final": True,
        }


class MemoryErrorStrategy(ErrorStrategy):
    """Стратегия для критических ошибок памяти - требует немедленного вмешательства"""

    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, MemoryError)

    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        return {
            "action": "fatal",
            "reason": f"Критическая ошибка памяти: {type(error).__name__}: {error}",
            "details": {"context": context, "error_type": type(error).__name__},
            "final": True,
        }


class UnicodeErrorStrategy(ErrorStrategy):
    """Стратегия для ошибок кодировки - могут быть исправлены перепаковкой"""

    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, (UnicodeError, UnicodeDecodeError, UnicodeEncodeError))

    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        return {
            "action": "quarantine",
            "reason": f"Ошибка кодировки: {type(error).__name__}: {error}",
            "details": {"context": context, "error_type": type(error).__name__},
            "quarantine_type": QuarantineType.ERROR,  # Используем перепаковку
            "final": True,
        }


class ConnectionErrorStrategy(ErrorStrategy):
    """Стратегия для ошибок соединения - можно повторить"""

    def can_handle(self, error: Exception) -> bool:
        return isinstance(error, (TimeoutError, ConnectionError))

    def handle(self, error: Exception, task_data: dict[str, Any], context: str) -> dict[str, Any]:
        # Возвращаем специальный флаг для повторной попытки
        return {
            "action": "retry_error",
            "error": ProcessingError(f"Ошибка соединения: {type(error).__name__}: {error}", ErrorType.RETRY),
            "task_data": task_data,
        }


class ErrorStrategyRegistry:
    """Реестр стратегий обработки ошибок"""

    def __init__(self):
        self._strategies: list[ErrorStrategy] = []
        self._register_default_strategies()

    def _register_default_strategies(self):
        """Регистрирует стандартные стратегии"""
        self.register(CorruptedArchiveStrategy())
        self.register(DatabaseErrorStrategy())
        self.register(SystemErrorStrategy())
        self.register(MemoryErrorStrategy())
        self.register(UnicodeErrorStrategy())
        self.register(ConnectionErrorStrategy())

    def register(self, strategy: ErrorStrategy):
        """Регистрирует новую стратегию"""
        self._strategies.append(strategy)

    def find_strategy(self, error: Exception) -> ErrorStrategy | None:
        """Находит подходящую стратегию для ошибки"""
        for strategy in self._strategies:
            if strategy.can_handle(error):
                return strategy
        return None
