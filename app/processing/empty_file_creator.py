# app/processing/empty_file_creator.py

import logging
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Any


class EmptyFileCreator:
    """Создатель пустых ZIP-маркеров для отслеживания обработанных файлов.

    Создает минимальные ZIP-файлы для типов карантина, где содержимое не нужно,
    но важно сохранить факт обработки для предотвращения повторной постановки в очередь.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_empty_marker(
        self,
        original_file: Path,
        target_file: Path,
        quarantine_reason: str = "Processed",
    ) -> dict[str, Any]:
        """Создает пустой ZIP-маркер на основе оригинального файла.

        Args:
            original_file: Путь к оригинальному файлу
            target_file: Путь к создаваемому маркеру
            quarantine_reason: Причина помещения в карантин

        Returns:
            Статистика создания: {'status': str, 'original_size': int, 'marker_size': int}
        """
        if not original_file.exists():
            raise FileNotFoundError(f"Оригинальный файл не найден: {original_file}")

        # Создаем родительскую директорию для целевого файла
        target_file.parent.mkdir(parents=True, exist_ok=True)

        original_size = original_file.stat().st_size

        try:
            # Создаем минимальный ZIP с метаинформацией
            with zipfile.ZipFile(target_file, "w", compression=zipfile.ZIP_DEFLATED, compresslevel=9) as marker_zip:
                # Создаем информационный файл о маркере
                marker_info = self._create_marker_info(original_file, quarantine_reason)
                marker_zip.writestr("_marker_info.txt", marker_info)

                # Добавляем пустой файл с именем оригинала для идентификации
                original_name = original_file.name
                if not original_name.endswith(".zip"):
                    original_name += ".zip"
                marker_zip.writestr(f"_original_{original_name}", "")

            marker_size = target_file.stat().st_size

            self.logger.info(
                f"Создан пустой маркер: {original_file.name} -> {target_file.name}. "
                f"Размер: {original_size:,} -> {marker_size:,} байт "
                f"(экономия: {((original_size - marker_size) / original_size):.1%})"
            )

            return {
                "status": "success",
                "original_size": original_size,
                "marker_size": marker_size,
                "space_saved": original_size - marker_size,
            }

        except Exception as e:
            self.logger.error(f"Ошибка при создании пустого маркера для {original_file}: {e}")
            # Удаляем частично созданный файл при ошибке
            if target_file.exists():
                target_file.unlink()
            raise

    def _create_marker_info(self, original_file: Path, quarantine_reason: str) -> str:
        """Создает информационное содержимое для маркера.

        Args:
            original_file: Оригинальный файл
            quarantine_reason: Причина помещения в карантин

        Returns:
            Текстовое содержимое информационного файла
        """
        now = datetime.now().isoformat()
        original_size = original_file.stat().st_size

        return f"""EMPTY MARKER FILE - PROCESSED BOOK ARCHIVE
========================================

Оригинальный файл: {original_file.name}
Размер оригинала: {original_size:,} байт
Причина карантина: {quarantine_reason}
Дата обработки: {now}
Тип маркера: EMPTY_ZIP_MARKER

ВНИМАНИЕ: Это пустой маркер, указывающий что файл был обработан
и больше не требует повторной обработки. Содержимое удалено для
экономии места. Не удаляйте этот файл - он предотвращает повторную
постановку в очередь.

Для восстановления очереди после сбоев этот файл критически важен.
"""

    def create_bulk_markers(self, file_mappings: list[tuple[Path, Path, str]]) -> dict[str, Any]:
        """Создает множество пустых маркеров в пакетном режиме.

        Args:
            file_mappings: Список кортежей (оригинал, цель, причина)

        Returns:
            Общая статистика обработки
        """
        total_originals = len(file_mappings)
        successful = 0
        failed = 0
        total_space_saved = 0

        self.logger.info(f"Начинаем пакетное создание {total_originals} пустых маркеров")

        for original_file, target_file, reason in file_mappings:
            try:
                result = self.create_empty_marker(original_file, target_file, reason)
                if result["status"] == "success":
                    successful += 1
                    total_space_saved += result["space_saved"]
                else:
                    failed += 1
            except Exception as e:
                self.logger.error(f"Ошибка при создании маркера для {original_file}: {e}")
                failed += 1

        self.logger.info(
            f"Пакетное создание маркеров завершено: "
            f"успешно={successful}, ошибок={failed}, "
            f"экономия места={total_space_saved:,} байт"
        )

        return {
            "total_files": total_originals,
            "successful": successful,
            "failed": failed,
            "total_space_saved": total_space_saved,
        }

    def is_empty_marker(self, file_path: Path) -> bool:
        """Проверяет, является ли файл пустым маркером.

        Args:
            file_path: Путь к проверяемому файлу

        Returns:
            True если файл является пустым маркером
        """
        if not file_path.exists() or not file_path.suffix.lower() == ".zip":
            return False

        try:
            with zipfile.ZipFile(file_path, "r") as zip_file:
                # Проверяем наличие характерных файлов маркера
                file_list = zip_file.namelist()

                has_marker_info = any(name == "_marker_info.txt" for name in file_list)
                has_original_ref = any(name.startswith("_original_") for name in file_list)

                # Маркер должен быть очень маленьким (менее 5KB)
                is_small = file_path.stat().st_size < 5120

                return has_marker_info and has_original_ref and is_small

        except Exception:
            return False

    def get_marker_info(self, marker_file: Path) -> dict[str, str] | None:
        """Извлекает информацию из пустого маркера.

        Args:
            marker_file: Путь к файлу маркера

        Returns:
            Словарь с информацией о маркере или None при ошибке
        """
        if not self.is_empty_marker(marker_file):
            return None

        try:
            with zipfile.ZipFile(marker_file, "r") as zip_file:
                if "_marker_info.txt" in zip_file.namelist():
                    info_content = zip_file.read("_marker_info.txt").decode("utf-8")

                    # Простой парсинг ключевых параметров
                    info = {}
                    for line in info_content.split("\n"):
                        if ":" in line and not line.startswith("="):
                            key, value = line.split(":", 1)
                            info[key.strip()] = value.strip()

                    return info

        except Exception as e:
            self.logger.warning(f"Ошибка при чтении информации маркера {marker_file}: {e}")

        return None
