from abc import ABC, abstractmethod
from typing import Any

from .error_handler import QuarantineType


class QuarantineStrategy(ABC):
    """Базовый класс стратегии обработки карантина"""

    @abstractmethod
    def can_handle(self, quarantine_type: QuarantineType) -> bool:
        """Проверяет, может ли стратегия обработать данный тип карантина"""
        pass

    @abstractmethod
    def get_processing_type(self) -> str:
        """Возвращает тип обработки: 'cold_storage', 'empty_marker', 'preserve'"""
        pass

    @abstractmethod
    def get_description(self) -> str:
        """Возвращает описание стратегии"""
        pass


class ColdStorageStrategy(QuarantineStrategy):
    """Стратегия холодного хранения для потенциально полезных файлов"""

    def can_handle(self, quarantine_type: QuarantineType) -> bool:
        return quarantine_type in (
            QuarantineType.ANTHOLOGIES,
            QuarantineType.FOOTNOTES,
            QuarantineType.ERROR,
            QuarantineType.SMALL_CONTENT,  # Книги с малым объемом - сохраняем для анализа
            QuarantineType.FEW_CHAPTERS,  # Книги с малым числом глав - могут быть ценными повестями
        )

    def get_processing_type(self) -> str:
        return "cold_storage"

    def get_description(self) -> str:
        return "Перепаковка для холодного хранения (потенциально полезные файлы)"


class EmptyMarkerStrategy(QuarantineStrategy):
    """Стратегия создания пустых маркеров для ненужных файлов"""

    def can_handle(self, quarantine_type: QuarantineType) -> bool:
        return quarantine_type in (QuarantineType.TRIAL, QuarantineType.INVALID)

    def get_processing_type(self) -> str:
        return "empty_marker"

    def get_description(self) -> str:
        return "Создание пустых маркеров (окончательно ненужные файлы)"


class PreserveStrategy(QuarantineStrategy):
    """Стратегия сохранения файлов как есть (резервная стратегия)"""

    def can_handle(self, quarantine_type: QuarantineType) -> bool:
        # Резервная стратегия - не обрабатывает ничего напрямую
        # Используется через fallback в реестре
        return False

    def get_processing_type(self) -> str:
        return "preserve"

    def get_description(self) -> str:
        return "Сохранение без изменений (резервная стратегия)"


class QuarantineStrategyRegistry:
    """Реестр стратегий обработки карантина"""

    def __init__(self):
        self._strategies: list[QuarantineStrategy] = []
        self._register_default_strategies()

    def _register_default_strategies(self):
        """Регистрирует стандартные стратегии"""
        self.register(ColdStorageStrategy())
        self.register(EmptyMarkerStrategy())
        self.register(PreserveStrategy())

    def register(self, strategy: QuarantineStrategy):
        """Регистрирует новую стратегию"""
        self._strategies.append(strategy)

    def find_strategy(self, quarantine_type: QuarantineType) -> QuarantineStrategy | None:
        """Находит подходящую стратегию для типа карантина"""
        for strategy in self._strategies:
            if strategy.can_handle(quarantine_type):
                return strategy
        return None

    def get_processing_type(self, quarantine_type: QuarantineType) -> str:
        """Получает тип обработки для типа карантина"""
        strategy = self.find_strategy(quarantine_type)
        if strategy:
            return strategy.get_processing_type()
        return "preserve"  # По умолчанию сохраняем как есть

    def get_all_strategies_info(self) -> dict[str, Any]:
        """Возвращает информацию о всех зарегистрированных стратегиях"""
        info = {}
        for quarantine_type in QuarantineType:
            strategy = self.find_strategy(quarantine_type)
            if strategy:
                info[quarantine_type.value] = {
                    "processing_type": strategy.get_processing_type(),
                    "description": strategy.get_description(),
                }
            else:
                info[quarantine_type.value] = {
                    "processing_type": "preserve",
                    "description": "Стратегия не найдена - сохранение по умолчанию",
                }
        return info
