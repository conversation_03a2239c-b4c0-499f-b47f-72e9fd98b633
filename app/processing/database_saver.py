# app/processing/database_saver.py

import json
import logging
from typing import Any

from app.database.connection import get_db_connection

from .dto import BookDTO, BookSourceInfo
from .error_handler import FatalError, ProcessingError


class DatabaseSaver:
    """Транзакционный сохранитель для атомарной записи всех данных книги в PostgreSQL.
    Все операции выполняются в рамках единой транзакции для обеспечения целостности.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def save_book(self, book_dto: BookDTO, book_id: str) -> str:
        """Сохраняет книгу и все связанные данные в БД в рамках единой транзакции.

        Args:
            book_dto: Объект с полными данными книги
            book_id: Уникальный идентификатор книги (должен быть сгенерирован заранее)

        Returns:
            Переданный book_id для подтверждения сохранения

        Raises:
            ProcessingError: При ошибках сохранения
            FatalError: При критических ошибках БД

        """
        return self._save_book_internal(book_dto, book_id, final_status=20)

    def save_book_metadata_only(self, book_dto: BookDTO, book_id: str) -> str:
        """Сохраняет только метаданные книги с промежуточным статусом (process_status=10).

        Используется в двухфазном сохранении для обеспечения атомарности.
        После создания артефакта статус обновляется через update_book_status().

        Args:
            book_dto: Объект с полными данными книги
            book_id: Уникальный идентификатор книги

        Returns:
            Переданный book_id для подтверждения сохранения

        Raises:
            ProcessingError: При ошибках сохранения
            FatalError: При критических ошибках БД

        """
        return self._save_book_internal(book_dto, book_id, final_status=10)

    def _save_book_internal(self, book_dto: BookDTO, book_id: str, final_status: int) -> str:
        """Внутренний метод для сохранения книги с указанным статусом."""
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    # Начинаем транзакцию (автоматически через контекст)

                    # 1. Сохраняем основную запись книги с указанным статусом
                    self._save_book_record(cur, book_id, book_dto, final_status)

                    # 2. Сохраняем информацию об источнике
                    if book_dto.source_info:
                        self._save_book_source(cur, book_id, book_dto.source_info)

                    # 3. Сохраняем авторов и связи
                    if book_dto.authors:
                        self._save_authors_and_relations(cur, book_id, book_dto.authors)

                    # 4. Сохраняем серии и связи
                    if book_dto.series:
                        self._save_series_and_relations(cur, book_id, book_dto)

                    # 5. Сохраняем жанры и связи
                    if book_dto.genres:
                        self._save_genres_and_relations(cur, book_id, book_dto.genres)

                    # 6. Сохраняем метаданные
                    self._save_book_metadata(cur, book_id, book_dto)

                    # Транзакция автоматически коммитится при выходе из контекста

                    status_desc = "метаданные сохранены" if final_status == 10 else "полностью обработано"
                    self.logger.info(f"Книга сохранена ({status_desc}): {book_id} - {book_dto.title}")

                    # ВАЖНО: явный commit, так как соединение возвращается в пул,
                    # и автокоммита при putconn нет.
                    conn.commit()

                    return book_id

        except Exception as e:
            self.logger.error(f"Ошибка сохранения книги в БД: {e}", exc_info=True)
            # Транзакция автоматически откатится при исключении

            # Анализируем тип ошибки
            error_msg = str(e).lower()
            if "connection" in error_msg or "server" in error_msg:
                raise FatalError(f"Критическая ошибка подключения к БД: {e}") from e
            else:
                raise ProcessingError(f"Ошибка сохранения в БД: {e}") from e

    def update_book_status(self, book_id: str, process_status: int) -> None:
        """Обновляет статус обработки книги.

        Используется в двухфазном сохранении для перевода статуса
        с "метаданные сохранены" (10) на "полностью обработано" (20).

        Args:
            book_id: Уникальный идентификатор книги
            process_status: Новый статус (обычно 20 - полностью обработано)

        Raises:
            ProcessingError: При ошибках обновления
            FatalError: При критических ошибках БД

        """
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cur:
                    query = """
                        UPDATE books 
                        SET process_status = %s, updated_at = NOW()
                        WHERE id = %s
                    """

                    cur.execute(query, (process_status, book_id))

                    if cur.rowcount == 0:
                        raise ProcessingError(f"Книга с ID {book_id} не найдена для обновления статуса")

                    # ВАЖНО: явный commit для гарантии сохранения изменений
                    conn.commit()

                    self.logger.debug(f"Статус книги {book_id} обновлен на {process_status}")

        except Exception as e:
            self.logger.error(f"Ошибка обновления статуса книги {book_id}: {e}")

            # Анализируем тип ошибки
            error_msg = str(e).lower()
            if "connection" in error_msg or "server" in error_msg:
                raise FatalError(f"Критическая ошибка подключения к БД: {e}") from e
            else:
                raise ProcessingError(f"Ошибка обновления статуса в БД: {e}") from e

    def _save_book_record(self, cur, book_id: str, book_dto: BookDTO, final_status: int):
        """Сохраняет основную запись в таблице books"""
        query = """
            INSERT INTO books (id, title, lang, metadata_hash, process_status)
            VALUES (%s, %s, %s, %s, %s)
        """

        cur.execute(
            query,
            (
                book_id,
                book_dto.title,
                book_dto.lang,
                book_dto.metadata_hash,
                final_status,
            ),
        )

    def _save_book_source(self, cur, book_id: str, source_info: BookSourceInfo):
        """Сохраняет информацию об источнике книги"""
        query = """
            INSERT INTO book_sources (book_id, source_type, source_id, metadata)
            VALUES (%s, %s, %s, %s)
        """

        cur.execute(
            query,
            (
                book_id,
                source_info.source_type,
                source_info.source_id,
                json.dumps(source_info.to_metadata_dict()),
            ),
        )

    def _save_authors_and_relations(self, cur, book_id: str, authors: list[dict[str, Any]]):
        """Сохраняет авторов и связи книга-автор"""
        for author_data in authors:
            # Ищем или создаем автора
            author_id = self._get_or_create_author(cur, author_data)

            # Создаем связь книга-автор
            query = """
                INSERT INTO book_authors (book_id, author_id, role_type)
                VALUES (%s, %s, %s)
                ON CONFLICT (book_id, author_id, role_type) DO NOTHING
            """

            cur.execute(query, (book_id, author_id, 1))  # role_type = 1 (автор)

    def _get_or_create_author(self, cur, author_data: dict[str, Any]) -> int:
        """Находит существующего автора или создает нового. АТОМАРНАЯ ОПЕРАЦИЯ.

        RACE CONDITION FIX: Использует CTE для полностью атомарной операции
        вместо уязвимого паттерна INSERT + SELECT. Гарантирует возврат ID
        в одной транзакции без возможности гонки состояний.
        """
        # Защитная обработка None значений - используем or "" для корректной работы strip()
        first_name = (author_data.get("first_name") or "").strip()
        last_name = (author_data.get("last_name") or "").strip()
        middle_name = (author_data.get("middle_name") or "").strip()
        nickname = (author_data.get("nickname") or "").strip()

        # Создаем метаданные автора
        metadata = {}
        if first_name and last_name:
            metadata["full_name"] = f"{last_name} {first_name} {middle_name}".strip()
        if nickname:
            metadata["nickname"] = nickname

        # Атомарная CTE-операция: пытаемся вставить, иначе находим существующего
        query = """
            WITH ins AS (
                INSERT INTO authors (first_name, last_name, middle_name, metadata)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (last_name, first_name, middle_name) DO NOTHING
                RETURNING id
            )
            SELECT id FROM ins
            UNION ALL
            SELECT id FROM authors
            WHERE last_name = %s AND first_name = %s AND middle_name = %s
            LIMIT 1;
        """

        cur.execute(
            query,
            (
                first_name,
                last_name,
                middle_name,
                json.dumps(metadata),  # для INSERT
                last_name,
                first_name,
                middle_name,  # для SELECT при конфликте
            ),
        )
        result = cur.fetchone()

        return result["id"]

    def _save_series_and_relations(self, cur, book_id: str, book_dto: BookDTO):
        """Сохраняет серии и связи книга-серия"""
        series_name = book_dto.series.strip()

        if not series_name:
            return

        # Ищем или создаем серию
        series_id = self._get_or_create_series(cur, series_name)

        # Создаем связь книга-серия
        query = """
            INSERT INTO book_series (book_id, series_id, series_number)
            VALUES (%s, %s, %s)
            ON CONFLICT (book_id, series_id) DO NOTHING
        """

        cur.execute(query, (book_id, series_id, book_dto.series_number))

        # --- Связь серия-автор (многие-ко-многим) ---
        # Для каждого автора книги создаём связь с серией.
        for author_data in book_dto.authors:
            author_id = self._get_or_create_author(cur, author_data)

            link_query = """
                INSERT INTO series_authors (series_id, author_id)
                VALUES (%s, %s)
                ON CONFLICT (series_id, author_id) DO NOTHING
            """

            cur.execute(link_query, (series_id, author_id))

    def _get_or_create_series(self, cur, series_name: str) -> int:
        """Находит существующую серию или создает новую. АТОМАРНАЯ ОПЕРАЦИЯ.

        RACE CONDITION FIX: Использует CTE для полностью атомарной операции
        вместо уязвимого паттерна INSERT + SELECT. Гарантирует возврат ID
        в одной транзакции без возможности гонки состояний.
        """
        # Атомарная CTE-операция: пытаемся вставить, иначе находим существующую
        query = """
            WITH ins AS (
                INSERT INTO series (name, type, metadata)
                VALUES (%s, %s, %s)
                ON CONFLICT (name, type) DO NOTHING
                RETURNING id
            )
            SELECT id FROM ins
            UNION ALL
            SELECT id FROM series
            WHERE name = %s AND type = %s
            LIMIT 1;
        """

        cur.execute(
            query,
            (
                series_name,
                1,
                json.dumps({}),  # для INSERT: type = 1 (авторская серия)
                series_name,
                1,  # для SELECT при конфликте
            ),
        )
        result = cur.fetchone()

        return result["id"]

    def _save_genres_and_relations(self, cur, book_id: str, genres: list[str]):
        """Сохраняет связи книга-жанр"""
        for genre_code in genres:
            # Ищем жанр в справочнике
            query = "SELECT genre_id FROM genrelist WHERE genre_code = %s LIMIT 1"
            cur.execute(query, (genre_code,))
            result = cur.fetchone()

            if result:
                genre_id = result["genre_id"]

                # Создаем связь книга-жанр
                link_query = """
                    INSERT INTO book_genres (book_id, genre_id)
                    VALUES (%s, %s)
                    ON CONFLICT (book_id, genre_id) DO NOTHING
                """
                cur.execute(link_query, (book_id, genre_id))

    def _save_book_metadata(self, cur, book_id: str, book_dto: BookDTO):
        """Сохраняет дополнительные метаданные книги.

        ВАЖНО: file_format НЕ сохраняется здесь - он принадлежит источнику
        и сохраняется в book_sources.metadata через BookSourceInfo.
        """
        # Собираем JSON для таблицы book_meta
        metadata = {
            "keywords": book_dto.keywords,
            "annotation": book_dto.annotation,
        }

        # Добавляем дополнительные метаданные, если они есть
        if book_dto.raw_metadata:
            metadata.update(book_dto.raw_metadata)

        query = """
            INSERT INTO book_meta (book_id, metadata)
            VALUES (%s, %s)
        """

        cur.execute(query, (book_id, json.dumps(metadata)))
