# app/processing/pruner.py

import logging
import re

from .canonical_model import CanonicalBook

logger = logging.getLogger(__name__)

# --- Набор регулярных выражений для очистки текста ---
# Паттерны компилируются один раз при загрузке модуля для максимальной производительности.
# Они нацелены на удаление стандартных "вотермарок" и мусора от конвертеров.
SANITIZATION_PATTERNS = [
    # Удаление информации об OCR и сканировании
    re.compile(r"ocr by\s.*$", re.IGNORECASE | re.MULTILINE),
    re.compile(r"scan by\s.*$", re.IGNORECASE | re.MULTILINE),
    re.compile(r"processed by\s.*$", re.IGNORECASE | re.MULTILINE),
    #
    # Удаление информации о создании файла
    re.compile(r"книга создана программой.*$", re.IGNORECASE | re.MULTILINE),
    re.compile(r"This file was created.*$", re.IGNORECASE | re.MULTILINE),
    re.compile(r"with BookDesigner program.*$", re.IGNORECASE | re.MULTILINE),
    re.compile(r"fb2convertor\.net", re.IGNORECASE),
    re.compile(r"bookdesigner.*$", re.IGNORECASE | re.MULTILINE),
    #
    # Удаление ссылок и упоминаний сайтов
    re.compile(r"https?://\S+", re.IGNORECASE),
    re.compile(r"www\..+\.(com|ru|net|org|is|lib|ec)", re.IGNORECASE),
    # re.compile(r"скачан[оа]\s+с\s+сайта\s+\S+", re.IGNORECASE),
    #
    # Удаление просьб об отзывах и подписках
    # ToDo: Реализовать удаление комментариев автора
    # re.compile(r"оставьте,?\s+пожалуйста,?\s+свой*$", re.IGNORECASE | re.MULTILINE),
    # re.compile(r"подписывайтесь\s+на\s+канал.*$", re.IGNORECASE | re.MULTILINE),
    re.compile(r"\*\*Продолжение\s+тут:.*", re.IGNORECASE | re.DOTALL),
    # re.compile(r"Продолжение\s+тут:.*?Спасибо\s+что\s+купили\s+книгу\)*",re.IGNORECASE | re.DOTALL,),
]

# Размер области на краях текста для очистки (в символах)
EDGE_CLEANUP_SIZE = 500


def _sanitize_text(text: str) -> str:
    """Применяет все регулярные выражения к одному куску текста."""
    if not text:
        return ""

    sanitized_text = text
    for pattern in SANITIZATION_PATTERNS:
        sanitized_text = pattern.sub("", sanitized_text)

    # Удаляем лишние пробелы и переносы строк, которые могли остаться после замен
    return sanitized_text.strip()


def _sanitize_text_content(content: str) -> str:
    """Очищает markdown содержимое от мусора.
    Возвращает очищенную строку.
    """
    return _sanitize_text(content)


def _sanitize_chapter_edges(content: str) -> str:
    """Эффективно очищает только края главы от OCR-мусора.

    Применяет очистку только к первым и последним EDGE_CLEANUP_SIZE символам,
    так как "вотермарки" и мусор OCR редко встречаются в середине текста.

    Args:
        content: Содержимое главы в формате Markdown

    Returns:
        Очищенное содержимое главы
    """
    if not content or len(content) <= EDGE_CLEANUP_SIZE * 2:
        # Если глава короткая, очищаем целиком
        return _sanitize_text_content(content)

    # Разделяем на начало, середину и конец
    start_part = content[:EDGE_CLEANUP_SIZE]
    middle_part = content[EDGE_CLEANUP_SIZE:-EDGE_CLEANUP_SIZE]
    end_part = content[-EDGE_CLEANUP_SIZE:]

    # Очищаем только края
    cleaned_start = _sanitize_text_content(start_part)
    cleaned_end = _sanitize_text_content(end_part)

    # Собираем обратно
    return cleaned_start + middle_part + cleaned_end


def prune_book(book: CanonicalBook) -> None:
    """Очищает объект CanonicalBook "на месте" (in-place), удаляя "мусор"
    из текстового содержимого.

    Применяет эффективную очистку ко всем главам, обрабатывая только первые
    и последние 500 символов каждой главы, где чаще всего встречается
    OCR-мусор и "вотермарки".

    Args:
        book: Объект CanonicalBook для очистки.
    """
    logger.debug(f"Начинаем очистку ('pruning') книги: '{book.title}'")

    # Очищаем аннотацию полностью (обычно короткая)
    if book.annotation_md:
        book.annotation_md = _sanitize_text_content(book.annotation_md)
        logger.debug("Очищена аннотация")

    # Очищаем края всех глав для удаления OCR-мусора по всей книге
    chapters_cleaned = 0
    for chapter in book.chapters:
        if chapter.content_md:
            original_length = len(chapter.content_md)
            chapter.content_md = _sanitize_chapter_edges(chapter.content_md)
            chapters_cleaned += 1

            # Логируем только если была существенная очистка
            cleaned_length = len(chapter.content_md)
            if original_length - cleaned_length > 50:
                logger.debug(f"Глава '{chapter.title}': удалено {original_length - cleaned_length} символов")

    logger.info(f"✅ Очистка книги '{book.title}' завершена. Обработано глав: {chapters_cleaned}")
