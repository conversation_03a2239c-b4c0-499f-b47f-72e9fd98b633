# app/processing/parsers/fb2/fb2_transformer.py

import logging
import re
from datetime import date
from typing import Union

from app.processing.canonical_model import (
    CanonicalAuthor,
    CanonicalBook,
    CanonicalChapter,
    CanonicalSequence,
)

from .fb2_model import (
    Annotation,
    BoldItalic,
    Cite,
    Code,
    Emphasis,
    FB2Book,
    Link,
    Note,
    Paragraph,
    Poem,
    Section,
    Strikethrough,
    Strong,
    Style,
    Subscript,
    Subtitle,
    Superscript,
)

logger = logging.getLogger(__name__)


class FB2Transformer:
    """Преобразует FB2-модель в каноническую, фокусируясь на преобразовании
    содержимого в чистый и правильный Markdown.
    """

    # Константа для фильтрации служебных разделов
    # Заголовки глав (в нижнем регистре, без пробелов), которые следует исключить
    # Поддерживает как точное совпадение, так и проверку по префиксам (startswith)
    # Например: "примечания" отфильтрует и "примечанияавтора", и "примечанияпереводчика"
    FOOTER_STOP_TITLES = {
        "notabene",
        "nota bene",
    }

    # Минимальный порог глав для успешного применения метода
    MIN_CHAPTER_THRESHOLD = 4

    def __init__(self, min_chapters_threshold: int = None):
        self.footnotes: dict[str, str] = {}  # Кэш сносок для текущей книги
        self.broken_footnotes: list[str] = []  # Список сломанных сносок для текущей книги
        # Используем новый порог или значение по умолчанию
        self.min_chapters_threshold = min_chapters_threshold or self.MIN_CHAPTER_THRESHOLD

        # === ЗАЩИТА ОТ ЦИКЛИЧЕСКИХ ССЫЛОК ===
        # Стек текущих inlined-ID и кэш уже отрендеренных параграфов
        self._inline_stack: set[str] = set()
        self._inline_cache: dict[str, str] = {}

        # НОВЫЙ ИЕРАРХИЧЕСКИЙ АЛГОРИТМ: приоритетные эвристики для поиска глав
        # 1. Приоритетный метод: стандартная разметка <section><title>
        # 2. Каскадные эвристики в порядке приоритета (только при провале п.1)
        self._chapter_heuristics = [
            self._heuristic_section_title,  # НОВЫЙ: приоритетный метод <section><title>
            self._heuristic_subtitle,  # Поиск по <subtitle>
            self._heuristic_paragraph_strong,  # <p><strong>Глава...</strong></p>
            self._heuristic_paragraph_text,  # <p>Глава...</p>
            self._heuristic_paragraph_emphasis,  # <p><emphasis>Глава...</emphasis></p>
            self._heuristic_separators,  # Разделители (***)
        ]

        # Регулярные выражения для определения заголовков глав
        self._chapter_patterns = [
            re.compile(r"^(глава|часть|пролог|эпилог|финал)\s*\d*$", re.IGNORECASE),
            re.compile(r"^\d+\.\s*(глава|часть).*$", re.IGNORECASE),
            re.compile(r"^(chapter|part|prologue|epilogue)\s*\d*$", re.IGNORECASE),
            re.compile(r"^\*+$"),  # Строки из звездочек как разделители
        ]

    def add_chapter_heuristic(self, heuristic_func, priority: int = None):
        """Добавляет новую эвристику поиска глав.

        Args:
            heuristic_func: Функция эвристики
            priority: Позиция в списке (None = добавить в конец)
        """
        if priority is None:
            self._chapter_heuristics.append(heuristic_func)
        else:
            self._chapter_heuristics.insert(priority, heuristic_func)
        logger.info(f"Добавлена эвристика: {heuristic_func.__name__}")

    def add_chapter_pattern(self, pattern: str, priority: int = None):
        """Добавляет новый паттерн для распознавания заголовков глав.

        Args:
            pattern: Регулярное выражение (строка)
            priority: Позиция в списке (None = добавить в конец)
        """
        compiled_pattern = re.compile(pattern, re.IGNORECASE)
        if priority is None:
            self._chapter_patterns.append(compiled_pattern)
        else:
            self._chapter_patterns.insert(priority, compiled_pattern)
        logger.info(f"Добавлен паттерн главы: {pattern}")

    def get_broken_footnotes(self) -> list[str]:
        """Возвращает список ID сломанных сносок из последней обработанной книги."""
        return self.broken_footnotes.copy()

    def has_broken_footnotes(self) -> bool:
        """Проверяет, есть ли сломанные сноски в последней обработанной книге."""
        return len(self.broken_footnotes) > 0

    def _build_id_map(self, bodies: list) -> None:
        """Строит карту всех параграфов с ID для быстрого поиска при встраивании сносок.

        Рекурсивно обходит все секции и параграфы в bodies и заполняет id_map
        парами {id: Paragraph} для последующего встраивания содержимого.
        """
        from .fb2_model import Body, Section

        def _scan_section(section: Section) -> None:
            """Рекурсивно сканирует секцию на предмет параграфов с ID."""
            for item in section.content:
                if isinstance(item, Section):
                    _scan_section(item)  # Рекурсивный обход вложенных секций
                elif isinstance(item, Paragraph) and item.id:
                    self.id_map[item.id] = item
                    logger.debug(f"Добавлен в карту ID: {item.id}")

        # Сканируем все bodies
        for body in bodies:
            if isinstance(body, Body):
                for section in body.sections:
                    _scan_section(section)

    def _render_content_to_markdown(
        self,
        content_item: Union[
            str,
            Emphasis,
            Strong,
            BoldItalic,
            Link,
            Subscript,
            Superscript,
            Strikethrough,
            Code,
            Style,
            Note,
            Paragraph,
        ],
    ) -> str:
        """Рекурсивно рендерит один элемент в Markdown-строку с полной поддержкой FB2 форматирования."""
        if isinstance(content_item, str):
            return content_item

        # Комбинированное форматирование
        if isinstance(content_item, BoldItalic):
            return f" ***{content_item.text.strip()}*** " if content_item.text else ""

        # Базовое форматирование
        if isinstance(content_item, Emphasis):
            return f" *{content_item.text.strip()}* " if content_item.text else ""
        if isinstance(content_item, Strong):
            return f" **{content_item.text.strip()}** " if content_item.text else ""

        # Специальное форматирование
        if isinstance(content_item, Subscript):
            # Markdown не поддерживает подстрочник нативно, используем HTML
            return f"<sub>{content_item.text.strip()}</sub>" if content_item.text else ""
        if isinstance(content_item, Superscript):
            # Markdown не поддерживает надстрочник нативно, используем HTML
            return f"<sup>{content_item.text.strip()}</sup>" if content_item.text else ""
        if isinstance(content_item, Strikethrough):
            return f" ~~{content_item.text.strip()}~~ " if content_item.text else ""
        if isinstance(content_item, Code):
            return f" `{content_item.text.strip()}` " if content_item.text else ""

        # Стили и сноски
        if isinstance(content_item, Style):
            # Для стилей добавляем HTML класс
            name_attr = f' class="{content_item.name}"' if content_item.name else ""
            return f"<span{name_attr}>{content_item.text.strip()}</span>" if content_item.text else ""
        if isinstance(content_item, Note):
            # RAG-оптимизированная обработка сносок: встраиваем текст сноски inline
            if content_item.href:
                # Извлекаем ID сноски из href="#note_1" -> "note_1"
                note_id = content_item.href.lstrip("#")

                # НОВАЯ ЛОГИКА: Проверяем, были ли вообще извлечены сноски
                if not self.footnotes:
                    # Если в книге нет описания с id сноски вообще, то это не ошибка
                    # Ссылка на сноску есть, а id ссылки на которое ссылается в документе вообще нет
                    logger.debug(f"Игнорируем ссылку на сноску {note_id}: сноски в книге отсутствуют")
                    return ""

                footnote_text = self.footnotes.get(note_id)

                if footnote_text:
                    # Встраиваем текст сноски для максимальной локальности контекста
                    logger.debug(f"Встраиваем сноску {note_id}: {footnote_text[:30]}...")
                    return f" (Сноска: {footnote_text.strip()}) "
                else:
                    # Если id из ссылки сноски в файле есть, но мы его не определили - это карантин по broken_footnotes
                    # Это сигнал проанализировать эти файлы и доработать нашу эвристику по определению сносок
                    logger.warning(f"Сломанная сноска {note_id}: есть сноски в файле, но не найден конкретный ID")
                    self.broken_footnotes.append(note_id)
                    return ""
            return ""

        # Ссылки
        if isinstance(content_item, Link):
            # Внутренняя ссылка (href начинается с "#")
            if content_item.href and content_item.href.startswith("#"):
                target_id = content_item.href.lstrip("#")

                # 1. Быстрый путь: есть готовый результат в кэше
                if target_id in self._inline_cache:
                    rendered_inline = self._inline_cache[target_id]
                # 2. Защита от самоссылок / циклов
                elif target_id in self._inline_stack:
                    rendered_inline = ""
                # 3. Рендерим параграф, если он есть в id_map
                elif target_id in self.id_map:
                    self._inline_stack.add(target_id)
                    target_paragraph = self.id_map[target_id]
                    self.inlined_ids.add(target_id)
                    rendered_inline = self._render_content_to_markdown(target_paragraph).strip()
                    self._inline_cache[target_id] = rendered_inline
                    self._inline_stack.remove(target_id)
                else:
                    rendered_inline = ""

                link_text = (content_item.text or "").strip()
                if link_text:
                    return f" {link_text} ({rendered_inline}) " if rendered_inline else f" {link_text} "
                return f" {rendered_inline} " if rendered_inline else ""

            # Внешняя ссылка или непонятный href – стандартный Markdown
            text = (content_item.text or content_item.href or "").strip()
            return f"[{text}]({content_item.href})" if content_item.href else ""

        # Рекурсивная обработка параграфов
        if isinstance(content_item, Paragraph):
            return "".join(self._render_content_to_markdown(p) for p in content_item.content)

        return ""

    def _clean_markdown_spacing(self, text: str) -> str:
        """Очищает лишние пробелы и артефакты в Markdown тексте."""
        # Заменяем мягкие переносы (soft hyphen \u00AD) на обычные пробелы
        text = text.replace("\u00ad", " ")
        text = text.replace("\u00a0", " ")

        # Убираем множественные пробелы
        text = re.sub(r" +", " ", text)
        # Убираем пробелы в начале и конце строк
        text = re.sub(r"^ +| +$", "", text, flags=re.MULTILINE)
        # Убираем пробелы вокруг знаков препинания
        text = re.sub(r" +([.,;:!?])", r"\1", text)
        return text

    def _convert_elements_to_markdown(self, elements: list) -> str:
        """Преобразует список FB2 элементов в единую Markdown строку."""
        md_parts = []
        for element in elements:
            if isinstance(element, Paragraph):
                # Пропускаем параграфы, которые уже были встроены как сноски
                if element.id and element.id in self.inlined_ids:
                    logger.debug(f"Пропускаем дублирование параграфа с ID {element.id}")
                    continue

                paragraph_text = self._render_content_to_markdown(element)
                paragraph_text = self._clean_markdown_spacing(paragraph_text.strip())
                md_parts.append(paragraph_text)
            elif isinstance(element, Poem):
                # Собираем содержимое поэмы
                poem_lines = []
                if element.title:
                    poem_lines.append(f"**{element.title}**")

                for stanza in element.stanzas:
                    stanza_lines = []
                    for line in stanza.lines:
                        # Защита от None в line.text
                        line_text = line.text or ""
                        if line_text.strip():  # Только непустые строки
                            stanza_lines.append(f"*{line_text.strip()}*")  # Курсив для стихов

                    if stanza_lines:  # Добавляем строфу только если есть содержимое
                        poem_lines.extend(stanza_lines)
                        poem_lines.append("")  # Пустая строка между строфами

                if element.text_author:
                    poem_lines.append(f"— {element.text_author}")

                # Убираем последнюю пустую строку и добавляем только если есть содержимое
                if poem_lines and poem_lines[-1] == "":
                    poem_lines.pop()

                if poem_lines:
                    md_parts.append("\n".join(poem_lines))
            elif isinstance(element, Cite):
                cite_content = self._convert_elements_to_markdown(element.paragraphs)
                indented_cite = "> " + cite_content.replace("\n\n", "\n> ")
                if element.text_author:
                    indented_cite += f"\n> — {element.text_author}"
                md_parts.append(indented_cite)
            elif isinstance(element, Subtitle):
                # Обрабатываем Subtitle как заголовок, но НЕ как маркер (это обработано в агрегаторе)
                subtitle_text = self._extract_marker_title(element)
                if subtitle_text.strip():
                    md_parts.append(f"**{subtitle_text.strip()}**")

        # Соединяем все части двойным переносом строки и очищаем лишние переносы
        full_text = "\n\n".join(filter(None, md_parts))
        # Убираем множественные переносы строк
        full_text = re.sub(r"\n{3,}", "\n\n", full_text)
        # Убираем любые случайные артефакты с символами кода
        full_text = re.sub(r"```+", "", full_text)  # Убираем тройные кавычки
        full_text = re.sub(r"``", "", full_text)  # Убираем двойные кавычки
        return full_text.strip()

    def _extract_clean_title(self, title_annotation: Annotation) -> str:
        """Извлекает заголовок и ОЧИЩАЕТ его от HTML-тегов."""
        if not title_annotation:
            return ""
        # Мы не рендерим Markdown, а просто собираем весь текст
        title_parts = []
        for element in title_annotation.elements:
            if isinstance(element, Paragraph):
                for content_part in element.content:
                    if isinstance(content_part, str):
                        title_parts.append(content_part)
                    elif hasattr(content_part, "text") and content_part.text:  # Emphasis, Strong, etc.
                        title_parts.append(content_part.text)
        return ". ".join(filter(None, [p.strip() for p in title_parts]))

    def _is_chapter_marker(self, element, heuristic_func=None) -> bool:
        """Определяет, является ли элемент маркером начала главы.

        Args:
            element: Элемент для проверки
            heuristic_func: Функция эвристики для применения (если None - стандартная проверка)
        """
        if heuristic_func:
            return heuristic_func(element)
        return isinstance(element, (Annotation, Subtitle))

    def _heuristic_section_title(self, element) -> bool:
        """Приоритетный метод: стандартная разметка <section><title>"""
        if not isinstance(element, Section):
            return False

        # Проверяем, есть ли заголовок в секции
        if not element.title or not element.title.elements:
            return False

        # Проверяем, есть ли заголовок в элементах секции
        for content_item in element.title.elements:
            if isinstance(content_item, str):
                title_text = content_item.strip()
                return self._matches_chapter_pattern(title_text)
        return False

    def _heuristic_subtitle(self, element) -> bool:
        """Поиск по <subtitle>"""
        if not isinstance(element, Subtitle):
            return False

        # Проверяем, есть ли текст в подзаголовке
        text_parts = []
        for content_item in element.content:
            if isinstance(content_item, str):
                text_parts.append(content_item.strip())
        subtitle_text = " ".join(filter(None, text_parts))
        return self._matches_chapter_pattern(subtitle_text) if subtitle_text.strip() else True

    def _heuristic_paragraph_strong(self, element) -> bool:
        """Эвристика: <p><strong>Глава N</strong></p>"""
        if not isinstance(element, Paragraph):
            return False

        if len(element.content) == 1 and isinstance(element.content[0], Strong):
            text = element.content[0].text.strip()
            return self._matches_chapter_pattern(text)
        return False

    def _heuristic_paragraph_text(self, element) -> bool:
        """Эвристика: <p>Глава N</p>"""
        if not isinstance(element, Paragraph):
            return False

        if len(element.content) == 1 and isinstance(element.content[0], str):
            text = element.content[0].strip()
            return self._matches_chapter_pattern(text)
        return False

    def _heuristic_paragraph_emphasis(self, element) -> bool:
        """Эвристика: <p><emphasis>Глава N</emphasis></p>"""
        if not isinstance(element, Paragraph):
            return False

        if len(element.content) == 1 and isinstance(element.content[0], Emphasis):
            text = element.content[0].text.strip()
            return self._matches_chapter_pattern(text)
        return False

    def _heuristic_separators(self, element) -> bool:
        """Эвристика: разделители (***)"""
        if not isinstance(element, str):
            return False

        # Проверяем, есть ли разделители в строке
        return "***" in element

    def _matches_chapter_pattern(self, text: str) -> bool:
        """Проверяет, соответствует ли текст паттерну заголовка главы"""
        text = text.strip()
        if not text:
            return False

        for pattern in self._chapter_patterns:
            if pattern.match(text):
                return True
        return False

    def _extract_marker_title(self, marker_element, heuristic_func=None) -> str:
        """Извлекает текст заголовка из маркера главы.

        Args:
            marker_element: Элемент маркера
            heuristic_func: Функция эвристики, которая определила этот маркер
        """
        if isinstance(marker_element, Section):
            # Для Section извлекаем заголовок из title
            if marker_element.title and marker_element.title.elements:
                text_parts = []
                for element in marker_element.title.elements:
                    if isinstance(element, str):
                        text_parts.append(element.strip())
                    elif hasattr(element, "content"):
                        # Для сложных элементов типа Paragraph
                        for content_item in element.content:
                            if isinstance(content_item, str):
                                text_parts.append(content_item.strip())
                            elif hasattr(content_item, "text") and content_item.text:
                                text_parts.append(content_item.text.strip())
                return " ".join(filter(None, text_parts))
            return f"Глава {id(marker_element)}"  # Fallback для Section без заголовка
        elif isinstance(marker_element, Annotation):
            return self._extract_clean_title(marker_element)
        elif isinstance(marker_element, Subtitle):
            # Обрабатываем Subtitle - извлекаем только текстовый контент
            text_parts = []
            for content_item in marker_element.content:
                if isinstance(content_item, str):
                    text_parts.append(content_item.strip())
            return " ".join(filter(None, text_parts))
        elif isinstance(marker_element, Paragraph):
            # Обрабатываем параграфы (для эвристик)
            if len(marker_element.content) == 1:
                content_item = marker_element.content[0]
                if isinstance(content_item, str):
                    return content_item.strip()
                elif hasattr(content_item, "text"):
                    return content_item.text.strip()
        elif isinstance(marker_element, str):
            # Для разделителей возвращаем номер главы
            return f"Глава {hash(marker_element) % 1000}"
        return ""

    def _flatten_sections_recursively(self, sections: list[Section]) -> list[CanonicalChapter]:
        """Рекурсивно преобразует дерево секций в плоский список глав.

        Реализует алгоритм согласно предложению:
        - Каждая секция становится отдельной главой
        - Заголовок секции = заголовок главы
        - Прямое содержимое секции = содержимое главы
        - Вложенные секции обрабатываются рекурсивно и добавляются после родительской

        Args:
            sections: Список секций верхнего уровня

        Returns:
            Плоский список глав в правильном порядке
        """
        chapters: list[CanonicalChapter] = []

        for section in sections:
            if not isinstance(section, Section):
                continue

            # Действие А: Обработка текущей секции
            # Извлекаем ТОЛЬКО прямое содержимое секции (не вложенные секции)
            direct_content: list = []
            nested_sections = []

            # Добавляем эпиграфы секции в прямое содержимое
            if section.epigraphs:
                direct_content.extend(section.epigraphs)

            # Разделяем прямое содержимое и вложенные секции
            for item in section.content:
                if isinstance(item, Section):
                    nested_sections.append(item)
                else:
                    direct_content.append(item)

            # Создаем главу из прямого содержимого ТОЛЬКО если есть реальное содержимое
            content_md = self._convert_elements_to_markdown(direct_content)

            # ИСПРАВЛЕНИЕ: создаем главы ТОЛЬКО при наличии содержимого
            if content_md.strip():
                # Извлекаем заголовок секции только для созданных глав
                if section.title and section.title.elements:
                    title_text = self._extract_marker_title(section)
                else:
                    # Секции без заголовков часто содержат служебную информацию
                    # Проверяем, является ли контент служебным (копирайт, ISBN и т.д.)
                    content_lower = content_md.lower()
                    service_keywords = [
                        "©",
                        "copyright",
                        "isbn",
                        "издательство",
                        "ridero",
                        "перевод с",
                        "переводчик",
                        "translator",
                        "translated",
                        "все права защищены",
                        "all rights reserved",
                        "редактор",
                        "корректор",
                        "дизайн обложки",
                    ]

                    if any(keyword in content_lower for keyword in service_keywords):
                        title_text = "Служебная информация"
                    else:
                        # Нумеруем только реально созданные главы без служебного контента
                        title_text = f"Глава {len(chapters) + 1}"

                chapters.append(CanonicalChapter(title=title_text, content_md=content_md))
                logger.debug(f"Создана глава: '{title_text}' ({len(content_md)} символов)")

            # Действие Б: Рекурсивная обработка вложенных секций
            if nested_sections:
                nested_chapters = self._flatten_sections_recursively(nested_sections)
                chapters.extend(nested_chapters)
                # Логируем только если были созданы главы
                if nested_chapters:
                    section_title = (
                        self._extract_marker_title(section)
                        if section.title and section.title.elements
                        else "безымянной секции"
                    )
                    logger.debug(f"Добавлено {len(nested_chapters)} вложенных глав из '{section_title}'")

        return chapters

    def _find_chapters_with_section_heuristic(self, all_sections: list) -> list[CanonicalChapter]:
        """Специализированный метод для поиска глав по стандартной разметке <section><title>

        НОВАЯ РЕАЛИЗАЦИЯ: использует рекурсивный алгоритм преобразования дерева секций
        """
        # Фильтруем только Section элементы
        sections = [s for s in all_sections if isinstance(s, Section)]

        if not sections:
            logger.debug("Нет секций для обработки рекурсивным алгоритмом")
            return []

        # Применяем новый рекурсивный алгоритм
        chapters = self._flatten_sections_recursively(sections)

        logger.info(f"🎯 Рекурсивный алгоритм секций: создано {len(chapters)} глав")

        return chapters

    def _find_chapters_with_heuristic(self, all_elements: list, heuristic_func) -> list[CanonicalChapter]:
        """Ищет главы используя конкретную эвристику (для элементарных маркеров)"""
        chapters = []
        current_chapter_content: list = []
        current_chapter_title = "Пролог"

        for element in all_elements:
            if self._is_chapter_marker(element, heuristic_func):
                # Сохраняем предыдущую главу, если есть контент
                if current_chapter_content:
                    content_md = self._convert_elements_to_markdown(current_chapter_content)
                    if content_md.strip():  # Только непустые главы
                        chapters.append(CanonicalChapter(title=current_chapter_title, content_md=content_md))

                # Начинаем новую главу
                current_chapter_content = []
                current_chapter_title = self._extract_marker_title(element, heuristic_func)
                if not current_chapter_title.strip():
                    current_chapter_title = f"Глава {len(chapters) + 1}"

            else:
                # Добавляем элемент в текущую главу
                current_chapter_content.append(element)

        # Сохраняем последнюю главу
        if current_chapter_content:
            content_md = self._convert_elements_to_markdown(current_chapter_content)
            if content_md.strip():
                chapters.append(CanonicalChapter(title=current_chapter_title, content_md=content_md))

        return chapters

    def _aggregate_chapters_from_elements(
        self, all_elements: list, all_sections: list = None
    ) -> list[CanonicalChapter]:
        """НОВЫЙ ИЕРАРХИЧЕСКИЙ АЛГОРИТМ определения глав.

        Алгоритм:
        1. Приоритетный метод: рекурсивное преобразование дерева секций (каждая секция = глава)
        2. Пороговый контроль: если найдено >= MIN_CHAPTER_THRESHOLD глав, результат считается достоверным
        3. Каскадные эвристики: применяются ТОЛЬКО при провале пп.1-2
        4. Fallback: если никто не дает >= порога, используется наилучший результат
        """
        best_result = []
        best_count = 0

        # 1. ПРИОРИТЕТНЫЙ МЕТОД: рекурсивное преобразование дерева секций
        if all_sections:
            priority_chapters = self._find_chapters_with_section_heuristic(all_sections)
        else:
            # Fallback на старый метод, если секции не переданы
            priority_chapters = self._find_chapters_with_heuristic(all_elements, self._heuristic_section_title)

        logger.info(f"🎯 Рекурсивный алгоритм дерева секций: найдено {len(priority_chapters)} глав")

        # Обновляем лучший результат
        if len(priority_chapters) > best_count:
            best_result = priority_chapters
            best_count = len(priority_chapters)

        # 2. ПОРОГОВЫЙ КОНТРОЛЬ: если приоритетный метод успешен, используем его
        if len(priority_chapters) >= self.MIN_CHAPTER_THRESHOLD:
            # logger.info(
            #     f"✅ Приоритетный метод успешен: {len(priority_chapters)} глав (порог: {self.MIN_CHAPTER_THRESHOLD})"
            # )
            return priority_chapters

        # 3. КАСКАДНЫЕ ЭВРИСТИКИ: применяем только при провале приоритетного метода
        # logger.warning(
        #     f"⚠️ Приоритетный метод дал {len(priority_chapters)} глав (< {self.MIN_CHAPTER_THRESHOLD}). Применяем эвристики..."
        # )

        for heuristic_func in self._chapter_heuristics[1:]:  # Пропускаем первую (приоритетную)
            chapters = self._find_chapters_with_heuristic(all_elements, heuristic_func)
            heuristic_name = heuristic_func.__name__.replace("_heuristic_", "")
            logger.debug(f"🔍 Эвристика '{heuristic_name}': найдено {len(chapters)} глав")

            # Обновляем лучший результат
            if len(chapters) > best_count:
                best_result = chapters
                best_count = len(chapters)

            # Применяем первую успешную эвристику
            if len(chapters) >= self.MIN_CHAPTER_THRESHOLD:
                # logger.info(f"✅ Применена эвристика '{heuristic_name}': {len(chapters)} глав")
                return chapters

        # 4. FALLBACK: используем наилучший результат, даже если он меньше порога
        # logger.warning(f"📖 Fallback: используется наилучший результат с {best_count} главами")
        return best_result

    def _filter_service_chapters(self, chapters: list[CanonicalChapter]) -> list[CanonicalChapter]:
        """Фильтрует служебные разделы по заголовкам.

        Удаляет главы, чьи заголовки соответствуют служебным разделам
        (примечания, информация от издательства и т.д.)

        Args:
            chapters: Список глав для фильтрации

        Returns:
            Отфильтрованный список глав без служебных разделов
        """
        filtered_chapters = []
        removed_count = 0

        for chapter in chapters:
            # Нормализуем заголовок: приводим к нижнему регистру и убираем пробелы
            normalized_title = re.sub(r"\s+", "", chapter.title.lower())

            # Проверяем, является ли заголовок служебным
            # Используем как точное совпадение, так и проверку префиксов для гибкости
            is_service = normalized_title in self.FOOTER_STOP_TITLES or any(
                normalized_title.startswith(stop_title) for stop_title in self.FOOTER_STOP_TITLES
            )

            if is_service:
                logger.debug(f"Исключена служебная глава: '{chapter.title}'")
                removed_count += 1
            else:
                filtered_chapters.append(chapter)

        if removed_count > 0:
            logger.info(f"✂️ Отфильтровано {removed_count} служебных глав")

        return filtered_chapters

    def _is_service_section(self, section: Section) -> bool:
        """Проверяет, является ли секция служебной по заголовку.

        Args:
            section: Секция FB2 для проверки

        Returns:
            True если секция служебная и должна быть удалена
        """
        if not section.title or not section.title.elements:
            return False

        # Извлекаем текст заголовка секции
        title_text = ""
        for element in section.title.elements:
            if hasattr(element, "content"):
                # Paragraph элемент
                for content_part in element.content:
                    if isinstance(content_part, str):
                        title_text += content_part
                    elif hasattr(content_part, "text") and content_part.text:
                        title_text += content_part.text
            elif isinstance(element, str):
                title_text += element

        # Нормализуем заголовок: приводим к нижнему регистру и убираем пробелы
        normalized_title = re.sub(r"\s+", "", title_text.lower())

        # Проверяем, является ли заголовок служебным
        is_service = normalized_title in self.FOOTER_STOP_TITLES or any(
            normalized_title.startswith(stop_title) for stop_title in self.FOOTER_STOP_TITLES
        )

        if is_service:
            logger.debug(f"Исключена служебная секция: '{title_text}'")

        return is_service

    def _filter_service_sections(self, sections: list) -> list:
        """Фильтрует служебные секции полностью.

        Удаляет целые секции FB2, чьи заголовки соответствуют служебным разделам.
        Это гарантирует, что весь контент служебной секции будет исключен.

        Args:
            sections: Список секций для фильтрации

        Returns:
            Отфильтрованный список секций без служебных разделов
        """
        from .fb2_model import Section

        filtered_sections = []
        removed_count = 0

        for section in sections:
            if isinstance(section, Section) and self._is_service_section(section):
                removed_count += 1
            else:
                filtered_sections.append(section)

        if removed_count > 0:
            logger.info(f"🗑️ Удалено {removed_count} служебных секций полностью")

        return filtered_sections

    def _flatten_section_elements(self, section: Section) -> list:
        """Рекурсивно разворачивает секцию в плоский список всех элементов.

        Включает title, epigraphs, content (кроме вложенных Section)
        """
        elements: list = []

        # Добавляем заголовок секции как маркер
        if section.title:
            elements.append(section.title)

        # Добавляем эпиграфы
        if section.epigraphs:
            elements.extend(section.epigraphs)

        # Обрабатываем контент
        for item in section.content:
            if isinstance(item, Section):
                # Рекурсивно разворачиваем вложенные секции
                elements.extend(self._flatten_section_elements(item))
            else:
                elements.append(item)
        return elements

    def transform(self, fb2_book: FB2Book, publication_date: date) -> CanonicalBook:
        # Инициализируем кэш сносок для текущей книги
        self.footnotes = fb2_book.footnotes
        self.broken_footnotes = []  # Сбрасываем список сломанных сносок

        # Инициализируем карту ID-элементов и реестр встроенных ID для обработки сносок
        self.id_map: dict[str, Paragraph] = {}
        self.inlined_ids: set[str] = set()

        # Строим карту всех элементов с ID перед началом обработки
        self._build_id_map(fb2_book.bodies)

        logger.info(f"Загружено {len(self.footnotes)} сносок для обработки")
        logger.info(f"Построена карта из {len(self.id_map)} элементов с ID")

        title_info = fb2_book.description.title_info
        if not title_info:
            raise ValueError("Отсутствует обязательная секция title_info в FB2")

        annotation_md = ""
        if title_info.annotation:
            annotation_md = self._convert_elements_to_markdown(title_info.annotation.elements)

        # ИНТЕЛЛЕКТУАЛЬНАЯ ЛОГИКА: приоритетные эвристики для автоматического обнаружения глав
        chapters = []
        for body in fb2_book.bodies:
            # ФИЛЬТРАЦИЯ СЕКЦИЙ: удаляем служебные секции целиком перед обработкой
            filtered_sections = self._filter_service_sections(body.sections)

            # Собираем плоский список всех элементов из отфильтрованных секций
            all_elements = []

            # Добавляем эпиграфы body, если есть
            all_elements.extend(body.epigraphs)

            # Разворачиваем все отфильтрованные секции в плоский список элементов
            for section in filtered_sections:
                all_elements.extend(self._flatten_section_elements(section))

            # Применяем НОВЫЙ ИЕРАРХИЧЕСКИЙ АЛГОРИТМ по маркерам глав
            body_chapters = self._aggregate_chapters_from_elements(all_elements, filtered_sections)
            chapters.extend(body_chapters)

        # СТРУКТУРНАЯ ФИЛЬТРАЦИЯ: удаляем служебные разделы после формирования всех глав
        chapters = self._filter_service_chapters(chapters)

        # Извлекаем жанры
        genres = [genre.text for genre in title_info.genres if genre.text]

        # Извлекаем ключевые слова
        keywords = []
        if title_info.keywords:
            # Разбиваем строку keywords по запятым и очищаем от лишних пробелов
            keywords = [kw.strip() for kw in title_info.keywords.split(",") if kw.strip()]

        # Извлекаем переводчиков
        translators = [
            CanonicalAuthor(t.first_name, t.middle_name, t.last_name, t.nickname) for t in title_info.translators
        ]

        # Создаем raw_metadata из description
        raw_metadata = {
            "title_info": {
                "book_title": title_info.book_title,
                "lang": title_info.lang,
                "src_lang": title_info.src_lang,
                "keywords": title_info.keywords,
                "genres": [{"text": g.text, "match": g.match} for g in title_info.genres],
                "sequences": [{"name": s.name, "number": str(s.number or 0)} for s in title_info.sequences],
            }
        }

        # Добавляем document_info если есть
        # if fb2_book.description.document_info:
        #     doc_info = fb2_book.description.document_info
        #     raw_metadata["document_info"] = {
        #         "program_used": doc_info.program_used,
        #         "version": doc_info.version,
        #         "doc_id": doc_info.doc_id,
        #     }
        # ВАЖНО: Не удалять. Раскомментировать при необходимости использования document_info.

        # Добавляем publish_info если есть
        if fb2_book.description.publish_info:
            pub_info = fb2_book.description.publish_info
            raw_metadata["publish_info"] = {
                "book_name": pub_info.book_name,
                "publisher": pub_info.publisher,
                "city": pub_info.city,
                "year": str(pub_info.year) if pub_info.year else "",
                "isbn": pub_info.isbn,
            }

        return CanonicalBook(
            title=title_info.book_title or "Без названия",
            lang=title_info.lang or "ru",
            source_format="fb2",
            authors=[CanonicalAuthor(a.first_name, a.middle_name, a.last_name, a.nickname) for a in title_info.authors],
            translators=translators,
            sequences=[CanonicalSequence(s.name, s.number or 0) for s in title_info.sequences],
            publication_date=publication_date,
            genres=genres,
            keywords=keywords,
            raw_metadata=raw_metadata,
            annotation_md=annotation_md,
            chapters=chapters,
        )
