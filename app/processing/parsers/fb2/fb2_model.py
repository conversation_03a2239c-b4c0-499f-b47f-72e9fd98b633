# app/processing/parsers/fb2/fb2_model.py

from dataclasses import dataclass, field
from typing import Optional, Union

# --- Базовые и переиспользуемые сущности ---


@dataclass
class Genre:
    text: str
    match: Optional[str] = None


@dataclass
class Author:
    first_name: Optional[str] = None
    middle_name: Optional[str] = None
    last_name: Optional[str] = None
    nickname: Optional[str] = None
    home_page: Optional[str] = None
    email: Optional[str] = None
    author_id: Optional[str] = None


@dataclass
class SequenceInfo:
    name: str
    number: Optional[int] = None


@dataclass
class DateInfo:
    value: Optional[str] = None
    text: Optional[str] = None


@dataclass
class Image:
    href: str
    content_type: Optional[str] = None
    alt: Optional[str] = None


@dataclass
class BinaryData:
    """Хранение бинарных данных (картинок) из FB2."""

    id: str  # ID из тега <binary>
    content_type: str
    data: bytes


# --- Структуры для аннотации (<annotation>) ---


@dataclass
class Link:
    href: str
    text: Optional[str] = None


@dataclass
class Emphasis:
    text: str


@dataclass
class Strong:
    text: str


@dataclass
class BoldItalic:
    text: str


# Добавляем поддержку дополнительных FB2 элементов форматирования
@dataclass
class Subscript:
    """Подстрочный текст <sub>"""

    text: str


@dataclass
class Superscript:
    """Надстрочный текст <sup>"""

    text: str


@dataclass
class Strikethrough:
    """Зачеркнутый текст <strikethrough>"""

    text: str


@dataclass
class Code:
    """Код <code>"""

    text: str


@dataclass
class Style:
    """Стилизованный текст <style>"""

    name: Optional[str]  # атрибут name
    text: str


@dataclass
class Note:
    """Сноска внутри параграфа"""

    href: str  # ссылка на сноску
    text: Optional[str] = None


ParagraphContent = Union[
    str,
    Emphasis,
    Strong,
    BoldItalic,
    Link,
    Subscript,
    Superscript,
    Strikethrough,
    Code,
    Style,
    Note,
]


@dataclass
class Paragraph:
    content: list[ParagraphContent] = field(default_factory=list)
    id: Optional[str] = None  # Атрибут id из FB2 элемента <p id="...">


@dataclass
class EmptyLine:
    pass


@dataclass
class Cite:
    paragraphs: list[Paragraph] = field(default_factory=list)
    text_author: Optional[str] = None


@dataclass
class Subtitle:
    content: list[str] = field(default_factory=list)


@dataclass
class PoemVerse:
    text: str


@dataclass
class PoemStanza:
    lines: list[PoemVerse] = field(default_factory=list)


@dataclass
class Poem:
    stanzas: list[PoemStanza] = field(default_factory=list)
    text_author: Optional[str] = None
    title: Optional[str] = None


AnnotationElement = Union[Paragraph, EmptyLine, Cite, Image, Link, Subtitle, Poem]


@dataclass
class Annotation:
    elements: list[AnnotationElement] = field(default_factory=list)


# --- Основные информационные блоки ---


@dataclass
class TitleInfo:
    genres: list[Genre] = field(default_factory=list)
    authors: list[Author] = field(default_factory=list)
    book_title: Optional[str] = None
    annotation: Optional[Annotation] = None
    keywords: Optional[str] = None
    date: Optional[DateInfo] = None
    coverpage: Optional[Image] = None
    lang: Optional[str] = None
    src_lang: Optional[str] = None
    translators: list[Author] = field(default_factory=list)
    sequences: list[SequenceInfo] = field(default_factory=list)


@dataclass
class DocumentInfo:
    authors: list[Author] = field(default_factory=list)
    program_used: Optional[str] = None
    date: Optional[DateInfo] = None
    src_urls: list[str] = field(default_factory=list)
    src_ocr: Optional[str] = None
    doc_id: Optional[str] = None
    version: Optional[str] = None


@dataclass
class PublishInfo:
    book_name: Optional[str] = None
    publisher: Optional[str] = None
    city: Optional[str] = None
    year: Optional[int] = None
    isbn: Optional[str] = None
    sequences: list[SequenceInfo] = field(default_factory=list)


@dataclass
class Description:
    """Корневой класс для метаданных из <description>."""

    title_info: Optional[TitleInfo] = None
    document_info: Optional[DocumentInfo] = None
    publish_info: Optional[PublishInfo] = None


# --- Структуры для тела книги (<body>) ---


@dataclass
class Section:
    """Представление секции <section>."""

    title: Optional[Annotation] = None  # Заголовки могут быть сложными
    epigraphs: list[Cite] = field(default_factory=list)
    content: list[Union["Section", AnnotationElement]] = field(default_factory=list)  # Секции могут быть вложенными


@dataclass
class Body:
    """Представление <body>."""

    name: Optional[str] = None
    title: Optional[Annotation] = None
    epigraphs: list[Cite] = field(default_factory=list)
    sections: list[Section] = field(default_factory=list)


# --- Корневой класс для всего FB2 файла ---


@dataclass
class FB2Book:
    """Полное представление FB2 файла."""

    description: Description
    bodies: list[Body] = field(default_factory=list)
    binaries: dict[str, BinaryData] = field(default_factory=dict)  # Словарь для быстрого доступа к картинкам по ID
    footnotes: dict[str, str] = field(default_factory=dict)  # Сноски: {id: текст_сноски}
