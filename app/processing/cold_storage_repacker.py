# app/processing/cold_storage_repacker.py

import logging
import re
import tempfile
import zipfile
from pathlib import Path
from typing import Any, Optional


class ColdStorageRepacker:
    """Перепаковщик для холодного хранения с удалением изображений из FB2.

    Оптимизирует файлы для долговременного хранения:
    - Удаляет все изображения из FB2 (теги <image> и <binary>)
    - Перепаковывает с максимальным сжатием
    - Сохраняет структуру для последующего перепарсивания
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Паттерны для удаления изображений из FB2
        self.binary_pattern = re.compile(r"<binary[^>]*>.*?</binary>", re.DOTALL | re.IGNORECASE)
        self.image_pattern = re.compile(r"<image[^>]*(?:\s*/>|>.*?</image>)", re.DOTALL | re.IGNORECASE)

    def repack_for_cold_storage(self, source_zip: Path, target_zip: Path) -> dict[str, Any]:
        """Перепаковывает ZIP-архив для холодного хранения.

        Args:
            source_zip: Исходный ZIP-файл
            target_zip: Целевой ZIP-файл для холодного хранения

        Returns:
            Статистика обработки: {'status': str, 'original_size': int, 'final_size': int, 'compression_ratio': float}
        """
        if not source_zip.exists():
            raise FileNotFoundError(f"Исходный файл не найден: {source_zip}")

        # Создаем родительскую директорию для целевого файла
        target_zip.parent.mkdir(parents=True, exist_ok=True)

        original_size = source_zip.stat().st_size

        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)

                # Извлекаем исходный архив
                with zipfile.ZipFile(source_zip, "r") as source_archive:
                    source_archive.extractall(temp_path)

                # Обрабатываем FB2 файлы
                fb2_files = list(temp_path.rglob("*.fb2"))
                processed_files = 0

                for fb2_file in fb2_files:
                    if self._clean_fb2_file(fb2_file):
                        processed_files += 1

                # Создаем новый архив с максимальным сжатием
                with zipfile.ZipFile(
                    target_zip,
                    "w",
                    compression=zipfile.ZIP_DEFLATED,
                    compresslevel=9,  # Максимальное сжатие
                ) as target_archive:
                    # Добавляем все файлы в архив
                    for file_path in temp_path.rglob("*"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(temp_path)
                            target_archive.write(file_path, arcname)

                final_size = target_zip.stat().st_size
                compression_ratio = (original_size - final_size) / original_size if original_size > 0 else 0

                self.logger.info(
                    f"Холодная перепаковка завершена: {source_zip.name} -> {target_zip.name}. "
                    f"FB2 файлов обработано: {processed_files}. "
                    f"Размер: {original_size:,} -> {final_size:,} байт "
                    f"(сжатие: {compression_ratio:.1%})"
                )

                return {
                    "status": "success",
                    "original_size": original_size,
                    "final_size": final_size,
                    "compression_ratio": compression_ratio,
                    "fb2_files_processed": processed_files,
                }

        except Exception as e:
            self.logger.error(f"Ошибка при холодной перепаковке {source_zip}: {e}")
            # Удаляем частично созданный файл при ошибке
            if target_zip.exists():
                target_zip.unlink()
            raise

    def _clean_fb2_file(self, fb2_path: Path) -> bool:
        """Удаляет изображения из FB2 файла.

        Args:
            fb2_path: Путь к FB2 файлу

        Returns:
            True если файл был изменен, False если изменений не было
        """
        try:
            # Читаем содержимое файла
            content = fb2_path.read_text(encoding="utf-8", errors="ignore")
            original_size = len(content)

            # Удаляем бинарные данные изображений
            content = self.binary_pattern.sub("", content)

            # Удаляем теги изображений
            content = self.image_pattern.sub("", content)

            final_size = len(content)

            # Сохраняем только если были изменения
            if final_size < original_size:
                fb2_path.write_text(content, encoding="utf-8")

                self.logger.debug(
                    f"Очищен FB2: {fb2_path.name}. "
                    f"Размер: {original_size:,} -> {final_size:,} символов "
                    f"(удалено: {((original_size - final_size) / original_size):.1%})"
                )
                return True

            return False

        except Exception as e:
            self.logger.warning(f"Ошибка при очистке FB2 файла {fb2_path}: {e}")
            return False

    def estimate_savings(self, source_zip: Path) -> Optional[dict[str, Any]]:
        """Оценивает потенциальную экономию места без фактической обработки.

        Args:
            source_zip: Исходный ZIP-файл

        Returns:
            Оценка экономии или None при ошибке
        """
        if not source_zip.exists():
            return None

        try:
            total_fb2_size = 0
            estimated_images_size = 0

            with zipfile.ZipFile(source_zip, "r") as archive:
                for file_info in archive.filelist:
                    if file_info.filename.lower().endswith(".fb2"):
                        try:
                            content = archive.read(file_info).decode("utf-8", errors="ignore")
                            total_fb2_size += len(content)

                            # Оценка размера изображений по количеству бинарных тегов
                            binary_matches = self.binary_pattern.findall(content)
                            estimated_images_size += sum(len(match) for match in binary_matches)

                        except Exception:
                            continue

            if total_fb2_size > 0:
                estimated_ratio = estimated_images_size / total_fb2_size
                return {
                    "total_fb2_size": total_fb2_size,
                    "estimated_images_size": estimated_images_size,
                    "estimated_savings_ratio": estimated_ratio,
                }

            return None

        except Exception as e:
            self.logger.warning(f"Ошибка при оценке экономии для {source_zip}: {e}")
            return None
