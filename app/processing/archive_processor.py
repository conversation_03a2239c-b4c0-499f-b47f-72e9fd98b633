# app/processing/archive_processor.py

import logging
import tempfile
import zipfile
from pathlib import Path

from .error_handler import ProcessingError, QuarantineError


class ArchiveProcessor:
    """Обработчик архивов для безопасного извлечения содержимого."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.max_extracted_size = 500 * 1024 * 1024
        self.max_files = 1000

    def _extract_zip_to_dir(self, zip_path: Path, destination_dir: Path):
        """Внутренний метод, который извлекает ZIP-архив в УКАЗАННУЮ директорию.
        Выполняет всю необходимую валидацию.

        Args:
            zip_path: Путь к ZIP-файлу.
            destination_dir: Путь к директории, куда будут извлечены файлы.

        """
        try:
            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                self._validate_zip_structure(zip_ref)
                zip_ref.extractall(destination_dir)
                self.logger.info(f"Архив '{zip_path.name}' успешно извлечен в '{destination_dir}'")
        except zipfile.BadZipFile as e:
            raise QuarantineError(f"Поврежденный ZIP архив: {e}") from e
        except Exception as e:
            if isinstance(e, (QuarantineError, ProcessingError)):
                raise
            raise ProcessingError(f"Ошибка при извлечении архива '{zip_path.name}': {e}") from e

    def extract_archive(self, archive_path: Path) -> Path:
        """Публичный метод. Создает СОБСТВЕННУЮ временную директорию и извлекает в нее архив.
        Используется производственным воркером.

        Returns:
            Path к созданной временной директории с извлеченными файлами.

        """
        if not archive_path.exists():
            raise ProcessingError(f"Архивный файл не найден: {archive_path}")

        suffix = archive_path.suffix.lower()
        if suffix != ".zip":
            raise QuarantineError(f"Неподдерживаемый тип архива: {suffix}")

        # Создаем временную директорию
        temp_dir = Path(tempfile.mkdtemp(prefix=f"book_extract_{archive_path.stem}_"))

        try:
            # Вызываем наш новый внутренний метод
            self._extract_zip_to_dir(archive_path, temp_dir)
            return temp_dir
        except Exception:
            # Очищаем временную директорию при ошибке
            self._cleanup_temp_dir(temp_dir)
            raise

    def _validate_zip_structure(self, zip_ref: zipfile.ZipFile):
        file_list = zip_ref.filelist
        if len(file_list) > self.max_files:
            raise QuarantineError(f"Слишком много файлов в архиве: {len(file_list)} > {self.max_files}")
        book_files = []
        for file_info in file_list:
            if ".." in file_info.filename or file_info.filename.startswith("/"):
                raise QuarantineError(f"Небезопасный путь в архиве: {file_info.filename}")
            if self._is_book_file(file_info.filename):
                book_files.append(file_info.filename)
        if not book_files:
            raise QuarantineError("В архиве не найдено книжных файлов")
        self.logger.debug(f"Валидация ZIP прошла успешно: {len(book_files)} книжных файлов")

    def find_book_files(self, extracted_dir: Path) -> list[Path]:
        priority_extensions = [".fb2", ".epub", ".mobi", ".azw", ".azw3"]
        found_files: dict[str, list[Path]] = {ext: [] for ext in priority_extensions}
        for file_path in extracted_dir.rglob("*"):
            if file_path.is_file():
                extension = file_path.suffix.lower()
                if extension in priority_extensions:
                    found_files[extension].append(file_path)
        result = []
        for ext in priority_extensions:
            group_files = sorted(found_files[ext], key=lambda x: x.stat().st_size, reverse=True)
            result.extend(group_files)
        self.logger.debug(
            f"📚 Найдено {len(result)} книжных файлов: FB2={len(found_files.get('.fb2', []))}, EPUB={len(found_files.get('.epub', []))}, MOBI={len(found_files.get('.mobi', [])) + len(found_files.get('.azw', [])) + len(found_files.get('.azw3', []))}"
        )
        return result

    def _is_book_file(self, filename: str) -> bool:
        book_extensions = {".fb2", ".epub", ".mobi", ".azw", ".azw3"}
        return Path(filename).suffix.lower() in book_extensions

    def _cleanup_temp_dir(self, temp_dir: Path):
        try:
            if temp_dir.exists():
                import shutil

                shutil.rmtree(temp_dir)
                self.logger.debug(f"Очищена временная директория: {temp_dir}")
        except Exception as e:
            self.logger.warning(f"Ошибка очистки временной директории {temp_dir}: {e}")
