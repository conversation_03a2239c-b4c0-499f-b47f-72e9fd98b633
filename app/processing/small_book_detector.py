# app/processing/small_book_detector.py

import logging
import os

from .canonical_model import CanonicalBook
from .error_handler import QuarantineType

logger = logging.getLogger(__name__)


class SmallBookDetector:
    """Детектор структурных аномалий книг с настраиваемыми критериями через переменные окружения.

    Определяет два типа структурных проблем:
    - Недостаточный объем контента (< MIN_CONTENT_CHARS) → SMALL_CONTENT
    - Аномально малое количество глав (< MIN_CHAPTERS) → FEW_CHAPTERS

    Логика проверки: сначала проверяется объем контента, затем структура глав.
    """

    def __init__(self):
        # Настраиваемые критерии через переменные окружения
        self.min_chapters = int(os.getenv("MIN_CHAPTERS", "4"))  # Минимальное количество глав
        self.min_content_chars = int(os.getenv("MIN_CONTENT_CHARS", "10000"))  # Всего символов в книге

        logger.debug(
            f"SmallBookDetector initialized: min_chapters={self.min_chapters}, "
            f"min_content_chars={self.min_content_chars}"
        )

    def _is_small_content(self, canonical_book: CanonicalBook) -> bool:
        """Проверяет, имеет ли книга недостаточный объем контента.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            True если общий объем контента меньше минимального порога
        """
        if not canonical_book.chapters:
            return True

        total_content = sum(len(ch.content_md) for ch in canonical_book.chapters)

        if total_content < self.min_content_chars:
            logger.debug(f"Недостаточный объем контента: {total_content} < {self.min_content_chars}")
            return True

        return False

    def _has_few_chapters(self, canonical_book: CanonicalBook) -> bool:
        """Проверяет, имеет ли книга аномально малое количество глав.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            True если количество глав меньше минимального порога
        """
        if not canonical_book.chapters:
            return False  # Книги без глав обрабатываются через _is_small_content

        chapters_count = len(canonical_book.chapters)

        if chapters_count < self.min_chapters:
            logger.debug(f"Аномально мало глав: {chapters_count} < {self.min_chapters}")
            return True

        return False

    def check_book_structure(self, canonical_book: CanonicalBook) -> QuarantineType | None:
        """Проверяет структуру книги и возвращает тип карантина если есть проблемы.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            QuarantineType если книга имеет структурные проблемы, None если все в порядке
        """
        # Приоритет 1: Проверка на недостаточный объем контента
        if self._is_small_content(canonical_book):
            return QuarantineType.SMALL_CONTENT

        # Приоритет 2: Проверка на малое количество глав (только если объем достаточный)
        if self._has_few_chapters(canonical_book):
            return QuarantineType.FEW_CHAPTERS

        return None  # Книга прошла все проверки

    def get_rejection_reason(self, canonical_book: CanonicalBook) -> str:
        """Возвращает детальную причину отклонения книги.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            Строка с описанием причины отклонения
        """
        quarantine_type = self.check_book_structure(canonical_book)

        if quarantine_type == QuarantineType.SMALL_CONTENT:
            if not canonical_book.chapters:
                return "Недостаточный объем контента: книга без глав"

            total_content = sum(len(ch.content_md) for ch in canonical_book.chapters)
            return f"Недостаточный объем контента: {total_content} символов < {self.min_content_chars}"

        elif quarantine_type == QuarantineType.FEW_CHAPTERS:
            chapters_count = len(canonical_book.chapters)
            return f"Аномально мало глав: {chapters_count} < {self.min_chapters}"

        else:
            return "Книга соответствует критериям структуры"

    def get_configuration(self) -> dict[str, int]:
        """Возвращает текущую конфигурацию детектора.

        Returns:
            Словарь с настройками
        """
        return {
            "min_chapters": self.min_chapters,
            "min_content_chars": self.min_content_chars,
        }
