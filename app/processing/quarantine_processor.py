# app/processing/quarantine_processor.py

import logging
from pathlib import Path
from typing import Any, Optional, cast

from .cold_storage_repacker import ColdStorageRepacker
from .empty_file_creator import EmptyFileCreator
from .error_handler import QuarantineType


class QuarantineProcessor:
    """Координатор постобработки файлов в карантине.

    Применяет различные стратегии в зависимости от типа карантина:
    - ANTHOLOGIES, FOOTNOTES: перепаковка для холодного хранения
    - TRIAL, ERROR, INVALID: создание пустых маркеров
    - SMALL: без дополнительной обработки (остается как есть)
    """

    def __init__(self, enable_processing: bool = True):
        self.logger = logging.getLogger(__name__)
        self.enable_processing = enable_processing

        if self.enable_processing:
            self.cold_storage_repacker = ColdStorageRepacker()
            self.empty_file_creator = EmptyFileCreator()
            self.logger.info("QuarantineProcessor инициализирован с включенной постобработкой")
        else:
            self.logger.info("QuarantineProcessor инициализирован с отключенной постобработкой")

        # Инициализируем реестр стратегий (ленивая загрузка чтобы избежать циклических импортов)
        self._strategy_registry: Any = None

    def process_quarantined_file(
        self,
        quarantine_file: Path,
        quarantine_type: QuarantineType,
        error_reason: str = "Unknown",
    ) -> Optional[dict[str, Any]]:
        """Обрабатывает файл в карантине согласно его типу.

        Args:
            quarantine_file: Путь к файлу в карантине
            quarantine_type: Тип карантина
            error_reason: Причина помещения в карантин

        Returns:
            Статистика обработки или None если обработка отключена/не применима
        """
        if not self.enable_processing:
            self.logger.debug(f"Постобработка карантина отключена для {quarantine_file.name}")
            return None

        if not quarantine_file.exists():
            self.logger.warning(f"Файл карантина не найден: {quarantine_file}")
            return None

        try:
            strategy = self._get_processing_strategy(quarantine_type)

            if strategy == "cold_storage":
                return self._process_for_cold_storage(quarantine_file, error_reason)
            elif strategy == "empty_marker":
                return self._process_as_empty_marker(quarantine_file, error_reason)
            elif strategy == "preserve":
                self.logger.debug(
                    f"Файл {quarantine_file.name} типа {quarantine_type.value} сохраняется без дополнительной обработки"
                )
                return {"status": "preserved", "strategy": "preserve"}
            else:
                self.logger.warning(f"Неизвестная стратегия обработки: {strategy}")
                return None

        except Exception as e:
            self.logger.error(
                f"Ошибка при постобработке файла карантина {quarantine_file}: {e}",
                exc_info=True,
            )
            return {"status": "error", "error": str(e)}

    def _get_strategy_registry(self):
        """Получает реестр стратегий (ленивая инициализация)"""
        if self._strategy_registry is None:
            from .quarantine_strategies import QuarantineStrategyRegistry

            self._strategy_registry = QuarantineStrategyRegistry()
        return self._strategy_registry

    def _get_processing_strategy(self, quarantine_type: QuarantineType) -> str:
        """Определяет стратегию обработки для типа карантина используя паттерн "Стратегия".

        ВАЖНО: Использует зарегистрированные стратегии для дифференциированной обработки:
        - ERROR (системные ошибки) → cold_storage (перепаковка с удалением картинок)
        - INVALID (поврежденные архивы) → empty_marker (обнуление)
        - ANTHOLOGIES, FOOTNOTES → cold_storage (потенциально пригодны)
        - TRIAL → empty_marker (окончательно не нужны)
        - остальные → preserve (сохраняются как есть)

        Args:
            quarantine_type: Тип карантина

        Returns:
            Название стратегии: 'cold_storage', 'empty_marker', 'preserve'
        """
        registry = self._get_strategy_registry()
        return registry.get_processing_type(quarantine_type)

    def _process_for_cold_storage(self, quarantine_file: Path, reason: str) -> dict[str, Any]:
        """Обрабатывает файл для холодного хранения."""
        # Создаем новое имя файла с суффиксом _cold
        cold_storage_file = quarantine_file.with_name(quarantine_file.stem + "_cold" + quarantine_file.suffix)

        self.logger.info(f"Начинаем холодную перепаковку: {quarantine_file.name} -> {cold_storage_file.name}")

        try:
            # Выполняем перепаковку
            result = self.cold_storage_repacker.repack_for_cold_storage(quarantine_file, cold_storage_file)

            # Удаляем оригинальный файл если перепаковка успешна
            if result.get("status") == "success":
                quarantine_file.unlink()
                self.logger.info(f"Оригинальный файл удален: {quarantine_file.name}")

                # Переименовываем холодный файл в оригинальное имя
                cold_storage_file.rename(quarantine_file)
                self.logger.info(f"Холодный файл переименован: {quarantine_file.name}")

                result.update(
                    {
                        "strategy": "cold_storage",
                        "reason": reason,
                        "final_file": str(quarantine_file),
                    }
                )

            return result

        except Exception:
            # Очищаем частично созданный файл при ошибке
            if cold_storage_file.exists():
                cold_storage_file.unlink()
            raise

    def _process_as_empty_marker(self, quarantine_file: Path, reason: str) -> dict[str, Any]:
        """Обрабатывает файл как пустой маркер."""
        # Создаем временное имя для маркера
        marker_file = quarantine_file.with_name(quarantine_file.stem + "_marker" + quarantine_file.suffix)

        self.logger.info(f"Создаем пустой маркер: {quarantine_file.name} -> {marker_file.name}")

        try:
            # Создаем пустой маркер
            result = self.empty_file_creator.create_empty_marker(quarantine_file, marker_file, reason)

            # Удаляем оригинальный файл если создание маркера успешно
            if result.get("status") == "success":
                quarantine_file.unlink()
                self.logger.info(f"Оригинальный файл удален: {quarantine_file.name}")

                # Переименовываем маркер в оригинальное имя
                marker_file.rename(quarantine_file)
                self.logger.info(f"Маркер переименован: {quarantine_file.name}")

                result.update(
                    {
                        "strategy": "empty_marker",
                        "reason": reason,
                        "final_file": str(quarantine_file),
                    }
                )

            return result

        except Exception:
            # Очищаем частично созданный файл при ошибке
            if marker_file.exists():
                marker_file.unlink()
            raise

    def get_processing_statistics(self, quarantine_dir: Path) -> dict[str, Any]:
        """Анализирует файлы в директории карантина и возвращает статистику.

        Args:
            quarantine_dir: Путь к директории карантина

        Returns:
            Статистика файлов по типам обработки
        """
        if not quarantine_dir.exists():
            return {"error": "Директория карантина не найдена"}

        stats: dict[str, Any] = {
            "total_files": 0,
            "by_strategy": {"cold_storage": 0, "empty_marker": 0, "preserve": 0},
            "by_type": {},
            "total_size": 0,
        }

        try:
            for quarantine_type in QuarantineType:
                type_dir = quarantine_dir / quarantine_type.value
                if type_dir.exists():
                    type_files = list(type_dir.rglob("*.zip"))
                    type_count = len(type_files)
                    type_size = sum(f.stat().st_size for f in type_files)

                    strategy = self._get_processing_strategy(quarantine_type)

                    stats["by_type"][quarantine_type.value] = {
                        "files": type_count,
                        "size": type_size,
                        "strategy": strategy,
                    }

                    stats["total_files"] = cast(int, stats["total_files"]) + type_count
                    stats["total_size"] = cast(int, stats["total_size"]) + type_size
                    by_strategy_dict = cast(dict[str, int], stats["by_strategy"])
                    by_strategy_dict[strategy] = by_strategy_dict[strategy] + type_count

            return stats

        except Exception as e:
            self.logger.error(f"Ошибка при анализе статистики карантина: {e}")
            return {"error": str(e)}

    def process_quarantine_directory(self, quarantine_dir: Path, dry_run: bool = False) -> dict[str, Any]:
        """Обрабатывает все файлы в директории карантина.

        Args:
            quarantine_dir: Путь к директории карантина
            dry_run: Если True, только анализирует без фактической обработки

        Returns:
            Общая статистика обработки
        """
        if not quarantine_dir.exists():
            return {"error": "Директория карантина не найдена"}

        results: dict[str, Any] = {
            "processed": 0,
            "errors": 0,
            "space_saved": 0,
            "by_strategy": {},
            "dry_run": dry_run,
        }

        self.logger.info(f"{'Анализируем' if dry_run else 'Обрабатываем'} директорию карантина: {quarantine_dir}")

        try:
            for quarantine_type in QuarantineType:
                type_dir = quarantine_dir / quarantine_type.value
                if not type_dir.exists():
                    continue

                strategy = self._get_processing_strategy(quarantine_type)

                if strategy not in results["by_strategy"]:
                    results["by_strategy"][strategy] = {"files": 0, "space_saved": 0}

                for zip_file in type_dir.rglob("*.zip"):
                    try:
                        if dry_run:
                            # Только анализ без обработки
                            results["processed"] += 1
                            results["by_strategy"][strategy]["files"] += 1
                        else:
                            # Фактическая обработка
                            result = self.process_quarantined_file(
                                zip_file,
                                quarantine_type,
                                f"Batch processing - {quarantine_type.value}",
                            )

                            if result and result.get("status") == "success":
                                results["processed"] += 1
                                space_saved = result.get("space_saved", 0)
                                results["space_saved"] += space_saved
                                results["by_strategy"][strategy]["files"] += 1
                                results["by_strategy"][strategy]["space_saved"] += space_saved
                            else:
                                results["errors"] += 1

                    except Exception as e:
                        self.logger.error(f"Ошибка обработки {zip_file}: {e}")
                        results["errors"] += 1

            self.logger.info(
                f"Обработка директории карантина завершена: "
                f"обработано={results['processed']}, ошибок={results['errors']}, "
                f"экономия={results['space_saved']:,} байт"
            )

            return results

        except Exception as e:
            self.logger.error(f"Критическая ошибка при обработке директории карантина: {e}")
            results["error"] = str(e)
            return results

    def copy_to_quarantine(
        self,
        source_file_path: Path,
        source_dir: Path,
        quarantine_type: QuarantineType = QuarantineType.ERROR,
        error_reason: str = None,
        claimed_at: str = None,
    ) -> Path:
        """Копирует проблемный файл в /quarantine/ с категоризацией (без перемещения исходного файла).

        Args:
            source_file_path: Путь к исходному файлу
            source_dir: Исходная директория для определения структуры папок
            quarantine_type: Тип карантина для создания подпапки
            error_reason: Причина помещения в карантин
            claimed_at: Время обработки задачи (для файла ошибки)

        Returns:
            Path к файлу в карантине
        """
        import shutil

        from app import settings

        dirs = settings.get_processing_directories(source_dir)

        # Вычисляем относительный путь с учетом типа карантина
        relative_path = source_file_path.relative_to(dirs["source"])
        quarantine_path = dirs["quarantine"] / quarantine_type.value / relative_path

        # Создаем промежуточные директории
        quarantine_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            # Копируем файл (НЕ перемещаем)
            shutil.copy2(str(source_file_path), str(quarantine_path))

            # Создаем файл с описанием ошибки внутри zip-архива
            if error_reason and quarantine_path.suffix.lower() == ".zip":
                try:
                    import zipfile
                    from tempfile import NamedTemporaryFile

                    # Создаем временный файл для записи ошибки
                    with NamedTemporaryFile(delete=False, suffix=".error") as temp_error_file:
                        error_content = f"Тип карантина: {quarantine_type.value}\nПричина: {error_reason}\nДата: {claimed_at or 'unknown'}"
                        temp_error_file.write(error_content.encode("utf-8"))
                        temp_error_path = Path(temp_error_file.name)

                    # Добавляем файл ошибки в zip-архив
                    with zipfile.ZipFile(quarantine_path, "a") as zip_file:
                        zip_file.write(temp_error_path, arcname=f"{quarantine_path.stem}.error")

                    # Удаляем временный файл
                    temp_error_path.unlink()

                except Exception as e:
                    self.logger.warning(f"Не удалось добавить файл ошибки в архив: {e}")

            self.logger.info(f"Файл скопирован в карантин: {source_file_path.name} → {quarantine_type.value}/")

            # Постобработка карантина - применяем соответствующую стратегию
            try:
                processing_result = self.process_quarantined_file(
                    quarantine_path, quarantine_type, error_reason or "Unknown"
                )

                if processing_result:
                    strategy = processing_result.get("strategy", "unknown")
                    if processing_result.get("status") in ("success", "preserved"):
                        space_saved = processing_result.get("space_saved", 0)
                        if space_saved > 0:
                            self.logger.info(
                                f"Постобработка карантина [{strategy}]: {quarantine_path.name}. "
                                f"Экономия места: {space_saved:,} байт"
                            )
                        else:
                            self.logger.debug(f"Постобработка карантина [{strategy}]: {quarantine_path.name}")
                    else:
                        self.logger.warning(
                            f"Ошибка постобработки карантина: {processing_result.get('error', 'Unknown')}"
                        )

            except Exception as e:
                self.logger.error(f"Ошибка постобработки карантина для {quarantine_path}: {e}")

            return quarantine_path

        except (FileNotFoundError, PermissionError) as e:
            self.logger.error(f"Ошибка копирования файла в карантин {source_file_path}: {e}")
            raise
