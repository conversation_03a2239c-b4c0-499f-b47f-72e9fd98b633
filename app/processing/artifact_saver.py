# app/processing/artifact_saver.py

import json
import logging
from pathlib import Path

import zstandard as zstd

from app import settings  # Импортируем настройки

from .canonical_model import CanonicalBook

logger = logging.getLogger(__name__)

# --- Конфигурация хранилища и сжатия ---

# Используем путь из настроек
STORAGE_BASE_DIR = settings.CANONICAL_STORAGE_PATH

# Уровень сжатия Zstandard. 15 - отличный баланс между высокой степенью сжатия
# и приемлемой скоростью. Для текста это даст превосходный результат.
ZSTD_COMPRESSION_LEVEL = 15

# TODO: Реализовать механизм обучения и использования словаря для Zstandard.
# На данном этапе для простоты словарь не используется, но архитектура готова к его добавлению.
# ZSTD_DICTIONARY = None


class ArtifactError(Exception):
    """Специализированное исключение для ошибок при работе с артефактами."""

    pass


def _get_artifact_path(book_id: str) -> Path:
    """Формирует многоуровневый путь к файлу артефакта на основе UUID книги.
    Это предотвращает хранение миллионов файлов в одной директории.

    Пример:
    book_id: 018f7b8c-a0f8-7177-8c6a-3a1b5b9d4a1e
    -> /storage/canonical_json/01/8f/7b/018f7b8c-a0f8-7177-8c6a-3a1b5b9d4a1e.json.zst

    Args:
        book_id: UUID книги (строка).

    Returns:
        Полный путь к файлу артефакта.

    """
    if not book_id or len(book_id) < 6:
        raise ValueError("Некорректный или слишком короткий book_id для формирования пути.")

    # Используем первые 3 пары символов для создания 3 уровней вложенности
    level1 = book_id[0:2]
    level2 = book_id[2:4]
    level3 = book_id[4:6]

    return STORAGE_BASE_DIR / level1 / level2 / level3 / f"{book_id}.json.zst"


def save_artifact(book: CanonicalBook, book_id: str) -> Path:
    """Сериализует, сжимает и сохраняет каноническую модель книги как артефакт.
    Операция атомарна: сначала создается временный файл, который затем
    переименовывается, чтобы избежать поврежденных артефактов при сбое.

    Args:
        book: Очищенный объект CanonicalBook.
        book_id: Сгенерированный UUIDv7 книги.

    Returns:
        Путь к сохраненному файлу артефакта.

    Raises:
        ArtifactError: В случае критической ошибки при сохранении.

    """
    artifact_path = _get_artifact_path(book_id)
    temp_path = artifact_path.with_suffix(artifact_path.suffix + ".tmp")

    logger.debug(f"Сохранение артефакта для book_id {book_id} в {artifact_path}")

    try:
        # --- 1. Создание директорий ---
        # parents=True создает все промежуточные директории.
        # exist_ok=True не вызовет ошибку, если директории уже существуют.
        artifact_path.parent.mkdir(parents=True, exist_ok=True)

        # --- 2. Сериализация и сжатие ---
        # Преобразуем dataclass в словарь для JSON-сериализации.
        # book_dict = asdict(book)
        book_dict = {
            "id": book_id,
            "book_id_info": {
                "book_id": book_id,
                "generation_date": book.book_id_generation_date,
                "date_source": book.book_id_date_source,
            },
            "source_info": {
                "source_id": book.source_id,
                "source_type": book.source_type,
            },
            "metadata": {
                "title": book.title,
                "lang": book.lang,
                "authors": [
                    {
                        "first_name": a.first_name,
                        "last_name": a.last_name,
                        "middle_name": a.middle_name,
                    }
                    for a in book.authors
                ],
                "translators": [
                    {
                        "first_name": t.first_name,
                        "last_name": t.last_name,
                        "middle_name": t.middle_name,
                    }
                    for t in book.translators
                ],
                "publication_date": str(book.publication_date),
                "genres": book.genres,
                "series": [{"name": s.name, "number": s.number} for s in book.sequences],
                "keywords": book.keywords,
                "annotation_md": book.annotation_md,
            },
            "chapters": [{"title": c.title, "content_md": c.content_md} for c in book.chapters],
        }

        # Кодируем JSON в байты для сжатия.
        # ensure_ascii=False важен для корректного сохранения кириллицы.
        # default=str для сериализации дат и других не-JSON объектов
        json_data = json.dumps(book_dict, ensure_ascii=False, default=str).encode("utf-8")

        # Инициализируем компрессор Zstandard.
        # В будущем здесь можно будет добавить словарь: cctx = zstd.ZstdCompressor(level=..., dict_data=...)
        cctx = zstd.ZstdCompressor(level=ZSTD_COMPRESSION_LEVEL)
        compressed_data = cctx.compress(json_data)

        # --- 3. Атомарная запись на диск ---
        # Сначала пишем во временный файл.
        with open(temp_path, "wb") as f:
            f.write(compressed_data)

        # Если запись прошла успешно, атомарно переименовываем его в основной.
        # Эта операция на большинстве файловых систем мгновенна и атомарна.
        temp_path.rename(artifact_path)

        logger.info(f"✅ Артефакт для '{book.title}' сохранен. Путь: {artifact_path}")
        return artifact_path

    except (IOError, OSError) as e:
        logger.error(f"Ошибка ввода-вывода при сохранении артефакта для book_id {book_id}: {e}")
        raise ArtifactError(f"Не удалось сохранить артефакт на диск: {e}") from e
    except Exception as e:
        logger.error(
            f"Неожиданная ошибка при создании артефакта для book_id {book_id}: {e}",
            exc_info=True,
        )
        # Попытка очистить временный файл, если он остался
        if temp_path.exists():
            temp_path.unlink(missing_ok=True)
        raise ArtifactError(f"Не удалось создать артефакт: {e}") from e
