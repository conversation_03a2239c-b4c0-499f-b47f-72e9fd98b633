# app/processing/anthology_detector.py

import logging
import os
import re

from .canonical_model import CanonicalBook

logger = logging.getLogger(__name__)


class AnthologyDetector:
    """Детектор антологий и сборников произведений разных авторов.

    Использует эвристики для выявления сборников:
    - Анализ заголовков глав на наличие имен авторов
    - Поиск ключевых слов "рассказ", "новелла", "повесть"
    - Обнаружение коротких глав от разных авторов
    - Проверка метаданных на множественных авторов
    """

    def __init__(self):
        # Настраиваемые критерии через переменные окружения
        self.min_chapters_for_anthology = int(os.getenv("ANTHOLOGY_MIN_CHAPTERS", "8"))
        self.max_avg_chapter_length = int(os.getenv("ANTHOLOGY_MAX_AVG_CHAPTER", "8000"))

        # ОТКЛЮЧЕНО: Проверка авторов в заголовках глав дает 99% ложных срабатываний
        # Паттерны плохо различают реальные имена авторов от обычных слов в заголовках
        # Может быть включена в будущем с улучшенными алгоритмами NLP
        # self.author_name_threshold = float(
        #     os.getenv("ANTHOLOGY_AUTHOR_THRESHOLD", "0.3")
        # )

        # Ключевые слова указывающие на сборник
        self.anthology_keywords = [
            "рассказ",
            "новелла",
            "повесть",
            "очерк",
            "миниатюра",
            "сказка",
            "басня",
            "эссе",
            "эскиз",
            "зарисовка",
            "story",
            "tale",
            "novelette",
            "short",
            "sketch",
            "сборник",
            "антология",
            "коллекция",
            "альманах",
            "collection",
            "anthology",
            "omnibus",
        ]

        # Паттерны заголовков глав с именами авторов
        self.author_patterns = [
            re.compile(r"^(.+?)\.\s*(.+)$"),  # "Автор. Название"
            re.compile(r"^(.+?):.*$"),  # "Автор: что-то"
            re.compile(r"^(.+?)\s*\(.*\).*$"),  # "Автор (что-то)"
            re.compile(r".*\s+—\s+(.+?)$"),  # "Название — Автор"
        ]

        # Общие имена для фильтрации ложных срабатываний
        self.common_words = {
            "глава",
            "часть",
            "пролог",
            "эпилог",
            "введение",
            "заключение",
            "начало",
            "конец",
            "финал",
            "chapter",
            "part",
            "prologue",
            "epilogue",
        }

    def is_anthology(self, canonical_book: CanonicalBook) -> bool:
        """Проверяет, является ли книга антологией/сборником.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            True если книга является антологией, False иначе
        """
        if not canonical_book.chapters:
            return False

        # Быстрая проверка по названию книги
        if self._check_title_keywords(canonical_book):
            logger.debug("Антология определена по ключевым словам в названии")
            return True

        # Проверка множественных авторов в метаданных
        if len(canonical_book.authors) > 3:
            logger.debug(f"Антология: слишком много авторов ({len(canonical_book.authors)})")
            return True

        # Основные эвристики анализа глав
        if len(canonical_book.chapters) >= self.min_chapters_for_anthology:
            # ОТКЛЮЧЕНО: Анализ заголовков глав на авторов (99% ложных срабатываний)
            # author_indicators = self._analyze_chapter_titles(canonical_book)

            # Анализируем структуру глав (короткие главы)
            structure_indicates = self._analyze_chapter_structure(canonical_book)

            # Используем только структурный анализ
            if structure_indicates:
                logger.debug(f"Антология: структура={structure_indicates}")
                return True

        return False

    def _check_title_keywords(self, canonical_book: CanonicalBook) -> bool:
        """Проверяет название книги на ключевые слова антологии."""
        if not canonical_book.title:
            return False

        title_lower = canonical_book.title.lower()
        return any(keyword in title_lower for keyword in self.anthology_keywords)

    def _analyze_chapter_titles(self, canonical_book: CanonicalBook) -> float:
        """Анализирует заголовки глав на наличие имен авторов.

        ОТКЛЮЧЕН: Метод сохранен для потенциального будущего использования
        с улучшенными алгоритмами распознавания имен авторов.

        Returns:
            Доля глав содержащих вероятные имена авторов (0.0 - 1.0)
        """
        if not canonical_book.chapters:
            return 0.0

        chapters_with_authors = 0

        for chapter in canonical_book.chapters:
            if not chapter.title:
                continue

            title = chapter.title.strip()

            # Пропускаем очевидно служебные заголовки
            if title.lower() in self.common_words:
                continue

            # Проверяем паттерны с авторами
            for pattern in self.author_patterns:
                if pattern.match(title):
                    chapters_with_authors += 1
                    break

            # Проверяем ключевые слова жанров
            title_lower = title.lower()
            if any(keyword in title_lower for keyword in self.anthology_keywords[:10]):  # Только жанровые слова
                chapters_with_authors += 1

        return chapters_with_authors / len(canonical_book.chapters)

    def _analyze_chapter_structure(self, canonical_book: CanonicalBook) -> bool:
        """Анализирует структуру глав (много коротких глав = возможный сборник).

        Returns:
            True если структура указывает на антологию
        """
        if len(canonical_book.chapters) < self.min_chapters_for_anthology:
            return False

        # Вычисляем среднюю длину главы
        total_content = sum(len(ch.content_md) for ch in canonical_book.chapters)
        avg_chapter_length = total_content / len(canonical_book.chapters)

        # Если главы в среднем короткие - возможно сборник
        if avg_chapter_length < self.max_avg_chapter_length:
            # Дополнительная проверка: много глав примерно одинакового размера
            chapter_lengths = [len(ch.content_md) for ch in canonical_book.chapters]

            # Проверяем, что большинство глав короткие (< 15000 символов)
            short_chapters = sum(1 for length in chapter_lengths if length < 4000)
            short_ratio = short_chapters / len(canonical_book.chapters)

            return short_ratio > 0.7  # 70% глав короткие

        return False

    def get_anthology_reason(self, canonical_book: CanonicalBook) -> str:
        """Возвращает детальную причину, почему книга считается антологией.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            Строка с описанием причины
        """
        if not self.is_anthology(canonical_book):
            return "Книга не является антологией"

        reasons = []

        if self._check_title_keywords(canonical_book):
            reasons.append("ключевые слова в названии")

        if len(canonical_book.authors) > 3:
            reasons.append(f"множественные авторы ({len(canonical_book.authors)})")

        if len(canonical_book.chapters) >= self.min_chapters_for_anthology:
            # ОТКЛЮЧЕНО: Проверка авторов в заголовках (99% ложных срабатываний)
            # author_indicators = self._analyze_chapter_titles(canonical_book)
            # if author_indicators >= self.author_name_threshold:
            #     reasons.append(f"авторы в заголовках глав ({author_indicators:.1%})")

            if self._analyze_chapter_structure(canonical_book):
                reasons.append("много коротких глав")

        return f"Антология: {', '.join(reasons)}"

    def get_configuration(self) -> dict:
        """Возвращает текущую конфигурацию детектора.

        Returns:
            Словарь с настройками
        """
        return {
            "min_chapters_for_anthology": self.min_chapters_for_anthology,
            "max_avg_chapter_length": self.max_avg_chapter_length,
            # "author_name_threshold": self.author_name_threshold,  # ОТКЛЮЧЕНО
            "anthology_keywords_count": len(self.anthology_keywords),
        }
