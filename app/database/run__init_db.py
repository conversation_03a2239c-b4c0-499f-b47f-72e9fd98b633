# run__init_db.py

import logging
from pathlib import Path

from app.database.connection import get_db_connection

# Настраиваем логирование
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def initialize_database():
    """Читает SQL-схему из файла и применяет ее к базе данных."""
    # Указываем путь к файлу схемы
    schema_path = Path(__file__).parent / "shema.sql"

    if not schema_path.exists():
        logging.critical(f"Schema file not found at: {schema_path}")
        return False

    logging.info(f"Found schema file at: {schema_path}")

    try:
        # Читаем весь SQL-скрипт в одну строку
        sql_script = schema_path.read_text(encoding="utf-8")

        if not sql_script.strip():
            logging.warning("Schema file is empty")
            return False

        logging.info("Connecting to the database to apply schema...")
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(sql_script)
            # Коммитим транзакцию, чтобы изменения сохранились
            conn.commit()

        logging.info("Database schema applied successfully!")
        return True

    except Exception as e:
        logging.critical(f"Failed to apply database schema: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    success = initialize_database()
    exit(0 if success else 1)
