# app/database/connection.py

from contextlib import contextmanager
from typing import Any, Iterator

import psycopg
from psycopg.rows import dict_row
from psycopg_pool import ConnectionPool

from app import settings


def configure_connection(conn: psycopg.Connection[Any]) -> None:
    """Настройка соединения для работы с dict_row."""
    conn.row_factory = dict_row


# Создаем пул соединений при старте приложения.
# min_size=2, max_size=10 означает, что пул будет держать от 2 до 10 открытых соединений.
# configure callback настраивает каждое соединение для возврата результатов как словари.
pool = ConnectionPool(
    conninfo=settings.DATABASE_URL,
    min_size=2,
    max_size=10,
    configure=configure_connection,
)


@contextmanager
def get_db_connection() -> Iterator[psycopg.Connection]:
    """Контекстный менеджер для получения соединения из пула.
    Гарантирует, что соединение будет возвращено в пул после использования.

    Пример использования:
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
    """
    conn = pool.getconn()
    try:
        yield conn
    finally:
        pool.putconn(conn)
