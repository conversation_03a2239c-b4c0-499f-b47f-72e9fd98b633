#!/usr/bin/env python3
"""
Демонстрация и тестирование системы постобработки карантина.

Показывает работу:
- ColdStorageRepacker для ANTHOLOGIES и FOOTNOTES
- EmptyFileCreator для TRIAL, ERROR, INVALID
- QuarantineProcessor как координатор

Использование:
    python tools/run_quarantine_demo.py --source-dir /path/to/source --dry-run
    python tools/run_quarantine_demo.py --source-dir /path/to/source --process
    python tools/run_quarantine_demo.py --stats-only
"""

import argparse
import logging
import sys
from pathlib import Path

# Настройка пути для импорта модулей проекта
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import settings
from app.processing.error_handler import QuarantineType
from app.processing.quarantine_processor import QuarantineProcessor


def setup_logging():
    """Настраивает логирование для демонстрации."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)],
    )


def demonstrate_quarantine_strategies():
    """Демонстрирует стратегии обработки карантина для каждого типа."""
    print("=" * 60)
    print("🔬 ДЕМОНСТРАЦИЯ СТРАТЕГИЙ ОБРАБОТКИ КАРАНТИНА")
    print("=" * 60)

    processor = QuarantineProcessor(enable_processing=True)

    strategies: dict[str, list[str]] = {}
    for quarantine_type in QuarantineType:
        strategy = processor._get_processing_strategy(quarantine_type)
        if strategy not in strategies:
            strategies[strategy] = []
        strategies[strategy].append(quarantine_type.value)

    strategy_descriptions = {
        "cold_storage": "🧊 Холодное хранение - удаление изображений, максимальное сжатие",
        "empty_marker": "📌 Пустые маркеры - минимальные ZIP-файлы для отслеживания",
        "preserve": "💾 Сохранение как есть - без дополнительной обработки",
    }

    for strategy, types in strategies.items():
        print(f"\n{strategy_descriptions.get(strategy, strategy)}")
        print(f"Типы: {', '.join(types)}")

    print("\n" + "=" * 60)


def analyze_quarantine_directory(quarantine_dir: Path) -> dict:
    """Анализирует директорию карантина и возвращает статистику."""
    if not quarantine_dir.exists():
        print(f"❌ Директория карантина не найдена: {quarantine_dir}")
        return {}

    processor = QuarantineProcessor(enable_processing=True)
    stats = processor.get_processing_statistics(quarantine_dir)

    if "error" in stats:
        print(f"❌ Ошибка анализа: {stats['error']}")
        return {}

    return stats


def print_quarantine_statistics(stats: dict):
    """Выводит статистику карантина в читаемом формате."""
    if not stats:
        return

    print("📊 СТАТИСТИКА КАРАНТИНА")
    print("=" * 40)
    print(f"Всего файлов: {stats['total_files']:,}")
    print(f"Общий размер: {stats['total_size']:,} байт ({stats['total_size'] / (1024 * 1024):.1f} MB)")

    print("\n📈 По стратегиям обработки:")
    for strategy, count in stats["by_strategy"].items():
        strategy_names = {
            "cold_storage": "🧊 Холодное хранение",
            "empty_marker": "📌 Пустые маркеры",
            "preserve": "💾 Сохранение как есть",
        }
        print(f"  {strategy_names.get(strategy, strategy)}: {count} файлов")

    print("\n📁 По типам карантина:")
    for type_name, type_stats in stats["by_type"].items():
        if type_stats["files"] > 0:
            size_mb = type_stats["size"] / (1024 * 1024)
            strategy_icons = {
                "cold_storage": "🧊",
                "empty_marker": "📌",
                "preserve": "💾",
            }
            icon = strategy_icons.get(type_stats["strategy"], "❓")

            print(f"  {icon} {type_name}: {type_stats['files']} файлов, {size_mb:.1f} MB")


def process_quarantine_directory(quarantine_dir: Path, dry_run: bool = False):
    """Обрабатывает файлы в директории карантина."""
    if not quarantine_dir.exists():
        print(f"❌ Директория карантина не найдена: {quarantine_dir}")
        return

    processor = QuarantineProcessor(enable_processing=True)

    print(f"{'🔍 АНАЛИЗ' if dry_run else '⚙️ ОБРАБОТКА'} ДИРЕКТОРИИ КАРАНТИНА")
    print("=" * 50)
    print(f"Директория: {quarantine_dir}")
    print(f"Режим: {'Только анализ (dry-run)' if dry_run else 'Полная обработка'}")
    print()

    results = processor.process_quarantine_directory(quarantine_dir, dry_run=dry_run)

    if "error" in results:
        print(f"❌ Ошибка обработки: {results['error']}")
        return

    print("📊 РЕЗУЛЬТАТЫ:")
    print(f"  Обработано файлов: {results['processed']}")
    print(f"  Ошибок: {results['errors']}")

    if not dry_run and results["space_saved"] > 0:
        space_saved_mb = results["space_saved"] / (1024 * 1024)
        print(f"  Экономия места: {results['space_saved']:,} байт ({space_saved_mb:.1f} MB)")

    if results["by_strategy"]:
        print("\n📈 По стратегиям:")
        for strategy, strategy_stats in results["by_strategy"].items():
            strategy_names = {
                "cold_storage": "🧊 Холодное хранение",
                "empty_marker": "📌 Пустые маркеры",
                "preserve": "💾 Сохранение как есть",
            }
            files = strategy_stats.get("files", 0)
            space_saved = strategy_stats.get("space_saved", 0)

            if files > 0:
                if space_saved > 0:
                    space_mb = space_saved / (1024 * 1024)
                    print(f"  {strategy_names.get(strategy, strategy)}: {files} файлов, экономия {space_mb:.1f} MB")
                else:
                    print(f"  {strategy_names.get(strategy, strategy)}: {files} файлов")


def main():
    """Главная функция демо-скрипта."""
    parser = argparse.ArgumentParser(
        description="Демонстрация системы постобработки карантина",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:

  # Показать стратегии обработки
  python tools/run_quarantine_demo.py --strategies

  # Анализ карантина для конкретного источника  
  python tools/run_quarantine_demo.py --source-dir /data/zip_flibusta --stats

  # Анализ без обработки (dry-run)
  python tools/run_quarantine_demo.py --source-dir /data/zip_flibusta --dry-run

  # Полная обработка карантина
  python tools/run_quarantine_demo.py --source-dir /data/zip_flibusta --process

  # Анализ всех источников
  python tools/run_quarantine_demo.py --all-sources --stats
        """,
    )

    parser.add_argument(
        "--source-dir",
        type=Path,
        help="Путь к директории источника (например, /data/zip_flibusta)",
    )

    parser.add_argument(
        "--all-sources",
        action="store_true",
        help="Обработать все источники из настроек",
    )

    parser.add_argument(
        "--strategies",
        action="store_true",
        help="Показать стратегии обработки для каждого типа карантина",
    )

    parser.add_argument("--stats", action="store_true", help="Показать только статистику карантина")

    parser.add_argument("--dry-run", action="store_true", help="Анализ без фактической обработки")

    parser.add_argument("--process", action="store_true", help="Выполнить полную обработку карантина")

    args = parser.parse_args()

    setup_logging()

    # Показать стратегии
    if args.strategies:
        demonstrate_quarantine_strategies()
        return

    # Определить источники для обработки
    source_dirs = []

    if args.all_sources:
        source_dirs = settings.SOURCE_DIRS
    elif args.source_dir:
        if args.source_dir.exists():
            source_dirs = [args.source_dir]
        else:
            print(f"❌ Директория не найдена: {args.source_dir}")
            return
    else:
        print("❌ Необходимо указать --source-dir или --all-sources")
        parser.print_help()
        return

    print("🎯 ПОСТОБРАБОТКА КАРАНТИНА")
    print(f"Настройка QUARANTINE_PROCESSING_ENABLED: {settings.QUARANTINE_PROCESSING_ENABLED}")
    print()

    # Обработать каждый источник
    for source_dir in source_dirs:
        quarantine_dir = source_dir / "quarantine"

        print(f"\n📂 ИСТОЧНИК: {source_dir.name}")
        print("-" * 40)

        if args.stats:
            # Только статистика
            stats = analyze_quarantine_directory(quarantine_dir)
            print_quarantine_statistics(stats)

        elif args.dry_run or args.process:
            # Анализ или обработка
            process_quarantine_directory(quarantine_dir, dry_run=args.dry_run)

        else:
            # По умолчанию - статистика
            stats = analyze_quarantine_directory(quarantine_dir)
            print_quarantine_statistics(stats)

    print("\n✅ Демонстрация завершена")


if __name__ == "__main__":
    main()
