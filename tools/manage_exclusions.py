#!/usr/bin/env python3
"""
🚫 УПРАВЛЕНИЕ ИСКЛЮЧЕНИЯМИ В РЕЕСТРЕ АНОМАЛИЙ

Простой CLI для добавления/удаления файлов из excluded_files.
Используется для исключения проверенных файлов из детекции аномалий.

Использование:
    # Добавить файл в исключения
    python tools/manage_exclusions.py add "/path/archive.zip::file.fb2"

    # Удалить из исключений
    python tools/manage_exclusions.py remove "/path/archive.zip::file.fb2"

    # Показать все исключения
    python tools/manage_exclusions.py list

    # Очистить все исключения
    python tools/manage_exclusions.py clear
"""

import sys
from pathlib import Path

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from tools.run_01_analyze_book_structure import (
    ANOMALY_REGISTRY_FILE,
    AnomalyPathRegistry,
)


def main():
    """Главная функция CLI."""
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)

    command = sys.argv[1].lower()
    registry = AnomalyPathRegistry(ANOMALY_REGISTRY_FILE)

    # Загружаем существующий реестр или создаем новый
    if not registry.load_registry():
        print("⚠️  Реестр не найден, создаем новый")

    if command == "add":
        if len(sys.argv) != 3:
            print("Использование: python manage_exclusions.py add '/path/archive.zip::file.fb2'")
            sys.exit(1)

        file_path = sys.argv[2]
        if "::" not in file_path:
            print("❌ Неверный формат пути. Используйте: /path/archive.zip::file.fb2")
            sys.exit(1)

        archive_path, fb2_filename = file_path.split("::", 1)
        registry.add_excluded_file(archive_path, fb2_filename)
        registry.save_registry()
        print(f"✅ Файл добавлен в исключения: {file_path}")

    elif command == "remove":
        if len(sys.argv) != 3:
            print("Использование: python manage_exclusions.py remove '/path/archive.zip::file.fb2'")
            sys.exit(1)

        file_path = sys.argv[2]
        if "::" not in file_path:
            print("❌ Неверный формат пути. Используйте: /path/archive.zip::file.fb2")
            sys.exit(1)

        archive_path, fb2_filename = file_path.split("::", 1)
        registry.remove_excluded_file(archive_path, fb2_filename)
        registry.save_registry()
        print(f"✅ Файл удален из исключений: {file_path}")

    elif command == "list":
        excluded_files = registry.registry_data.get("excluded_files", [])
        if not excluded_files:
            print("📝 Исключенных файлов нет")
        else:
            print(f"📝 Исключенных файлов: {len(excluded_files)}")
            for file_path in excluded_files:
                print(f"   {file_path}")

    elif command == "clear":
        excluded_count = registry.get_excluded_count()
        registry.registry_data["excluded_files"] = []
        registry.save_registry()
        print(f"✅ Очищено исключений: {excluded_count}")

    else:
        print(f"❌ Неизвестная команда: {command}")
        print(__doc__)
        sys.exit(1)


if __name__ == "__main__":
    main()
