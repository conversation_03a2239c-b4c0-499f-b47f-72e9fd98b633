# Структура метаданных FB2 книг

**Обработано FB2 файлов:** 13249

- description
  - custom-info (text content) @info-type
    - purchased @value
    - status @value
  
  - document-info
    - annotation
      - p (text content)
    - author (text content)
      - email (text content)
      - first-name (text content)
      - home-page (text content)
      - id (text content)
      - last-name (text content)
      - middle-name (text content)
      - nickname (text content)
    - date (text content) @lang @value
    - history (text content)
      - cite
        - p (text content)
      - p (text content)
        - a (text content) @href
          - strong (text content)
        - emphasis (text content)
        - image @href
        - strong (text content)
        - style (text content) @name
          - a (text content) @href
    - id (text content)
    - program-used (text content)
    - publisher
      - first-name (text content)
      - id (text content)
      - last-name (text content)
      - middle-name (text content)
    - src-ocr (text content)
    - src-url (text content)
    - version (text content)

  - publish-info
    - book-name (text content)
    - city (text content)
    - isbn (text content)
    - publisher (text content)
    - sequence @name @number
      - sequence @name
    - year (text content)
  - src-title-info
    - annotation
      - p (text content)
    - author
      - first-name (text content)
      - home-page (text content)
      - id (text content)
      - last-name (text content)
      - middle-name (text content)
    - book-title (text content)
    - coverpage
      - image @href
    - date (text content) @value
    - genre (text content)
    - keywords (text content)
    - lang (text content)
    - sequence @name @number
    - src-lang (text content)
    - translator
      - home-page (text content)
      - nickname (text content)

  - title-info
    - annotation (text content)
      - a (text content) @href @style
        - nnotation
      - annotation
        - p (text content)
          - a (text content) @href
      - cite
        - p (text content)
          - emphasis (text content)
          - style @name
            - emphasis (text content)
        - text-author (text content)
          - emphasis (text content)
      - image @href
      - p (text content) @id @style
        - a (text content) @href @id @name @rel @target @type
          - strong (text content)
        - br @class @data-n
        - emphasis (text content)
          - a (text content) @href
          - emphasis (text content)
            - a (text content) @href
          - strong (text content)
        - h3 (text content)
        - image @href
        - oem
        - strikethrough (text content)
        - strong (text content)
          - emphasis (text content)
        - style (text content) @name
          - a (text content) @href
          - emphasis (text content)
          - style (text content) @name
        - sup (text content)
      - poem
        - stanza
          - v (text content)
        - text-author (text content)
      - strong (text content)
        - a (text content) @href
        - strong
          - a (text content) @href
      - subtitle (text content)
        - emphasis (text content)
        - image @href
        - strong (text content)
        - style @name
          - image @href
    - author
      - email (text content)
      - first-name (text content)
      - home-page (text content)
      - id (text content)
      - last-name (text content)
      - middle-name (text content)
      - nickname (text content)
      - username (text content)
    - book-title (text content)
    - coverpage
      - image @alt @href @type
    - date (text content) @value
    - genre (text content) @match
    - keywords (text content)
    - lang (text content)
    - p (text content)
    - sequence @name @number
      - sequence @name @number
        - sequence @name @number
    - src-lang (text content)
    - translator
      - email (text content)
      - first-name (text content)
      - home-page (text content)
      - id (text content)
      - last-name (text content)
      - middle-name (text content)
      - nickname (text content)
