"""Utils - Пакет вспомогательных инструментов для разработки

⚠️  ВНИМАНИЕ: КРИТИЧЕСКИ ВАЖНАЯ ПАПКА!
🚫 НЕ УДАЛЯТЬ! НЕ ОЧИЩАТЬ! НЕ ПЕРЕМЕЩАТЬ!

Этот пакет содержит незаменимые инструменты для:
- Тестирования и диагностики pipeline обработки книг
- Анализа метаданных FB2/EPUB файлов
- Отладки парсеров и трансформеров
- Исследования структур данных книжных форматов

Все скрипты в этой папке активно используются в процессе разработки
и необходимы для поддержания качества кода.

Статус: ПОСТОЯННЫЙ ПАКЕТ РАЗРАБОТКИ
"""

# Явно помечаем как пакет инструментов разработки
__version__ = "1.0.0"
__purpose__ = "development_tools"
__status__ = "permanent"
__warning__ = "DO_NOT_DELETE_OR_CLEANUP"

# Список основных инструментов
DEVELOPMENT_TOOLS = [
    "run_02_test_pipeline_02.py",  # Полная диагностика pipeline_02
    "run_01_analyze_fb2_metadata.py",  # Анализ FB2 метаданных из секции <description>
    "run_01_analyze_epub_metadata.py",  # Анализ EPUB метаданных
    "run_02_analyze_fb2_schema_discovery.py",  # Исследование FB2 схем на основе реальных книг
]

# Выходные файлы анализа
ANALYSIS_RESULTS = [
    "result_fb2_structure_analyzer__opus.md",  # Результаты анализа FB2 схем
    "result_diagnostic_pipeline02_output.json",  # Результаты диагностики pipeline_02
]
