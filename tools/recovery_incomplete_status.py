#!/usr/bin/env python3
"""
Инструмент восстановления зависших статусов книг.

Обнаруживает книги со статусом process_status=10 (метаданные сохранены),
но без соответствующих JSON-артефактов на диске.

Использование:
    python tools/recovery_incomplete_status.py --dry-run  # Только анализ
    python tools/recovery_incomplete_status.py --fix      # Восстановление
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Any, cast

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.database.connection import get_db_connection
from app.processing.artifact_saver import _get_artifact_path


def check_incomplete_books() -> list[tuple[str, str]]:
    """Находит книги с промежуточным статусом без артефактов.

    Returns:
        Список кортежей (book_id, title) проблемных книг
    """
    incomplete_books = []

    with get_db_connection() as conn:
        with conn.cursor() as cur:
            # Ищем книги со статусом 10 (метаданные сохранены)
            cur.execute(
                """
                SELECT id, title 
                FROM books 
                WHERE process_status = 10
                ORDER BY updated_at DESC
            """
            )

            results = cur.fetchall()

            for row in results:
                row_dict = cast(dict[str, Any], row)
                book_id = str(row_dict["id"])
                title = str(row_dict["title"])

                # Проверяем существование артефакта
                artifact_path = _get_artifact_path(book_id)
                if not artifact_path.exists():
                    incomplete_books.append((book_id, title))

    return incomplete_books


def fix_incomplete_status(book_id: str) -> bool:
    """Обновляет статус книги на 0 для повторной обработки.

    Args:
        book_id: ID книги для исправления

    Returns:
        True если успешно обновлено
    """
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    UPDATE books 
                    SET process_status = 0, updated_at = NOW()
                    WHERE id = %s AND process_status = 10
                """,
                    (book_id,),
                )

                return cur.rowcount > 0

    except Exception as e:
        logging.error(f"Ошибка обновления статуса для {book_id}: {e}")
        return False


def main():
    """Главная функция для анализа и восстановления."""
    parser = argparse.ArgumentParser(description="Восстановление зависших статусов книг")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--dry-run", action="store_true", help="Только анализ без изменений")
    group.add_argument("--fix", action="store_true", help="Исправить найденные проблемы")

    args = parser.parse_args()

    # Настройка логирования
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

    # Поиск проблемных книг
    print("🔍 Поиск книг с промежуточным статусом...")
    incomplete_books = check_incomplete_books()

    if not incomplete_books:
        print("✅ Все книги в порядке - зависших статусов не найдено")
        return

    print(f"⚠️  Найдено {len(incomplete_books)} книг с промежуточным статусом:")
    print("-" * 60)

    for book_id, title in incomplete_books:
        print(f"📚 {book_id}: {title[:50]}...")

    if args.dry_run:
        print("\n🔍 Режим анализа - изменения не применены")
        print("💡 Для исправления запустите: python tools/recovery_incomplete_status.py --fix")
        return

    # Режим исправления
    print(f"\n🔧 Исправление {len(incomplete_books)} книг...")
    fixed_count = 0

    for book_id, _title in incomplete_books:
        if fix_incomplete_status(book_id):
            print(f"✅ Исправлено: {book_id}")
            fixed_count += 1
        else:
            print(f"❌ Ошибка: {book_id}")

    print(f"\n📊 Результат: {fixed_count}/{len(incomplete_books)} книг исправлено")
    print("💡 Исправленные книги будут переобработаны при следующем запуске воркера")


if __name__ == "__main__":
    main()
