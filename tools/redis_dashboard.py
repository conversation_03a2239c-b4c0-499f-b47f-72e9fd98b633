#!/usr/bin/env python3
"""
Комплексная панель управления диагностикой Redis.

Единая точка доступа к:
- Моментальному анализу (redis_diagnostic.py)
- Мониторингу в реальном времени (redis_monitor.py)
- Очистке и управлению данными
- Экспорту отчетов

Использование:
python tools/redis_dashboard.py
"""

import argparse
import json
import subprocess
import sys
import time
from pathlib import Path

import redis

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import settings


class RedisDashboard:
    """Панель управления диагностикой Redis."""

    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.tools_dir = Path(__file__).parent

    def show_menu(self):
        """Показывает главное меню."""

        print("\n🔍 REDIS DASHBOARD - КОМПЛЕКСНАЯ ДИАГНОСТИКА")
        print("=" * 60)
        print("1. 📊 Моментальный анализ состояния")
        print("2. 🔄 Мониторинг в реальном времени")
        print("3. 📈 Детальный анализ с экспортом JSON")
        print("4. 🧹 Очистка данных Redis")
        print("5. 📋 Просмотр структуры данных")
        print("6. ⚡ Быстрая статистика")
        print("7. 🚨 Проверка проблем")
        print("8. 💾 Экспорт всех данных")
        print("0. ❌ Выход")
        print("=" * 60)

        choice = input("Выберите действие (0-8): ").strip()
        return choice

    def instant_analysis(self):
        """Моментальный анализ состояния."""

        print("\n🔍 Запуск моментального анализа...")
        subprocess.run([sys.executable, str(self.tools_dir / "redis_diagnostic.py")])

    def real_time_monitor(self):
        """Мониторинг в реальном времени."""

        print("\n🔄 Настройка мониторинга:")
        interval = input("Интервал обновления в секундах (по умолчанию 5): ").strip()
        if not interval or not interval.isdigit():
            interval = "5"

        duration = input("Длительность в секундах (Enter для бесконечного): ").strip()

        cmd = [
            sys.executable,
            str(self.tools_dir / "redis_monitor.py"),
            "--interval",
            interval,
        ]
        if duration and duration.isdigit():
            cmd.extend(["--duration", duration])

        print(f"\n🚀 Запуск мониторинга с интервалом {interval}с...")
        subprocess.run(cmd)

    def detailed_analysis(self):
        """Детальный анализ с экспортом."""

        timestamp = int(time.time())
        output_file = f"redis_report_{timestamp}.json"

        print("\n📈 Запуск детального анализа...")
        subprocess.run(
            [
                sys.executable,
                str(self.tools_dir / "redis_diagnostic.py"),
                "--detailed",
                "--export-json",
                str(self.tools_dir / output_file),
            ]
        )

        print(f"\n💾 Отчет сохранен: tools/{output_file}")

    def cleanup_redis(self):
        """Очистка данных Redis."""

        print("\n🧹 ОЧИСТКА ДАННЫХ REDIS")
        print("⚠️  ВНИМАНИЕ: Это удалит все данные в Redis!")
        print("\nДоступные действия:")
        print("1. Очистить все очереди")
        print("2. Очистить только завершенные задачи")
        print("3. Очистить кэши (sets)")
        print("4. ПОЛНАЯ ОЧИСТКА")
        print("0. Отмена")

        choice = input("\nВыберите действие (0-4): ").strip()

        if choice == "0":
            return

        confirm = input("Введите 'YES' для подтверждения: ").strip()
        if confirm != "YES":
            print("❌ Операция отменена")
            return

        try:
            if choice == "1":
                # Очистка очередей
                queues = [
                    settings.QUEUE_PARSING_NEW,
                    settings.QUEUE_PARSING_PROCESSING,
                    settings.QUEUE_COMPLETED,
                    settings.QUEUE_CHUNKING_NEW,
                    settings.QUEUE_CHUNKING_PROCESSING,
                    settings.QUEUE_ENRICH_NEW,
                    settings.QUEUE_ENRICH_PROCESSING,
                    settings.QUEUE_VECTORIZE_NEW,
                    settings.QUEUE_VECTORIZE_PROCESSING,
                ]
                for queue in queues:
                    count = self.redis_client.llen(queue)
                    if count > 0:
                        self.redis_client.delete(queue)
                        print(f"✅ Очищена очередь {queue}: {count} элементов")
                    else:
                        print(f"🟢 Очередь {queue} уже пуста")

            elif choice == "2":
                # Очистка только завершенных
                count = self.redis_client.llen(settings.QUEUE_COMPLETED)
                if count > 0:
                    self.redis_client.delete(settings.QUEUE_COMPLETED)
                    print(f"✅ Очищена очередь завершенных задач: {count} элементов")
                else:
                    print("🟢 Очередь завершенных задач уже пуста")

            elif choice == "3":
                # Очистка кэшей
                sets = [settings.SET_QUEUED_IDS]  # SET_PROCESSED упразднен
                for set_name in sets:
                    count = self.redis_client.scard(set_name)
                    if count > 0:
                        self.redis_client.delete(set_name)
                        print(f"✅ Очищен set {set_name}: {count} элементов")
                    else:
                        print(f"🟢 Set {set_name} уже пуст")

            elif choice == "4":
                # Полная очистка
                self.redis_client.flushdb()
                print("✅ Выполнена полная очистка Redis")

            print("\n🎉 Очистка завершена!")

        except Exception as e:
            print(f"❌ Ошибка очистки: {e}")

    def view_data_structure(self):
        """Просмотр структуры данных."""

        print("\n📋 СТРУКТУРА ДАННЫХ REDIS")
        print("-" * 40)

        try:
            # Все ключи
            keys = self.redis_client.keys("*")
            if not keys:
                print("🟢 Redis пуст")
                return

            print(f"📊 Всего ключей: {len(keys)}")
            print()

            for key in sorted(keys):
                key_str = key.decode() if isinstance(key, bytes) else str(key)
                key_type = self.redis_client.type(key).decode()

                if key_type == "list":
                    size = self.redis_client.llen(key)
                    print(f"📋 {key_str:30} | LIST  | {size:4d} элементов")

                    # Показываем первый элемент
                    if size > 0:
                        first = self.redis_client.lindex(key, 0)
                        if first:
                            try:
                                data = json.loads(first.decode())
                                if isinstance(data, dict):
                                    keys_str = ", ".join(list(data.keys())[:5])
                                    print(f"    🔧 Структура: {keys_str}")
                            except Exception:
                                print(f"    📝 Пример: {str(first)[:50]}...")

                elif key_type == "set":
                    size = self.redis_client.scard(key)
                    print(f"🗂️  {key_str:30} | SET   | {size:4d} элементов")

                    # Показываем образец
                    if size > 0:
                        sample = self.redis_client.srandmember(key)
                        if sample:
                            sample_str = sample.decode() if isinstance(sample, bytes) else str(sample)
                            print(f"    📝 Образец: {sample_str[:50]}")

                else:
                    print(f"❓ {key_str:30} | {key_type.upper():5} | неизвестный тип")

                print()

        except Exception as e:
            print(f"❌ Ошибка просмотра структуры: {e}")

    def quick_stats(self):
        """Быстрая статистика."""

        print("\n⚡ БЫСТРАЯ СТАТИСТИКА")
        print("-" * 30)

        try:
            # Очереди
            parsing_new = self.redis_client.llen(settings.QUEUE_PARSING_NEW)
            parsing_processing = self.redis_client.llen(settings.QUEUE_PARSING_PROCESSING)
            completed = self.redis_client.llen(settings.QUEUE_COMPLETED)
            chunking_new = self.redis_client.llen(settings.QUEUE_CHUNKING_NEW)

            # Сеты
            processed_files = 0  # SET_PROCESSED упразднен в новой архитектуре
            queued_ids = self.redis_client.scard(settings.SET_QUEUED_IDS)

            # Redis info
            info = self.redis_client.info()
            memory_mb = info.get("used_memory", 0) / (1024 * 1024)

            print("🔢 ОЧЕРЕДИ:")
            print(f"   🆕 Парсинг новые:   {parsing_new:4d}")
            print(f"   ⚙️  Парсинг процесс: {parsing_processing:4d}")
            print(f"   ✅ Завершенные:     {completed:4d}")
            print(f"   🤖 Чанкинг новые:   {chunking_new:4d}")
            print()
            print("📦 КЭШИ:")
            # SET_PROCESSED считается устаревшим, поэтому явно помечаем его как legacy,
            # чтобы убрать возможную путаницу с новой архитектурой Scanner-Inventorizer.
            print(f"   📝 Кэш обработанных (legacy): {processed_files:4d}")
            print(f"   🆔 ID в очередях:   {queued_ids:4d}")
            print()
            print("💻 СИСТЕМА:")
            print(f"   🧠 Память:          {memory_mb:5.1f} MB")
            print(f"   ⚡ Команд/сек:      {info.get('instantaneous_ops_per_sec', 0):4d}")

            # Простая проверка консистентности
            total_parsing = parsing_new + parsing_processing
            if abs(queued_ids - total_parsing) > 1:
                print(f"\n⚠️  НЕСООТВЕТСТВИЕ: ID в сете ({queued_ids}) != задачи парсинга ({total_parsing})")
            else:
                print("\n✅ Консистентность очередей в порядке")

        except Exception as e:
            print(f"❌ Ошибка получения статистики: {e}")

    def check_problems(self):
        """Проверка проблем."""

        print("\n🚨 ПРОВЕРКА ПРОБЛЕМ")
        print("-" * 30)

        problems = []

        try:
            # Сбор метрик
            parsing_new = self.redis_client.llen(settings.QUEUE_PARSING_NEW)
            parsing_processing = self.redis_client.llen(settings.QUEUE_PARSING_PROCESSING)
            completed = self.redis_client.llen(settings.QUEUE_COMPLETED)
            chunking_new = self.redis_client.llen(settings.QUEUE_CHUNKING_NEW)
            queued_ids = self.redis_client.scard(settings.SET_QUEUED_IDS)
            processed_files = 0  # SET_PROCESSED упразднен в новой архитектуре

            # Проверки
            if parsing_processing > 10:
                problems.append(f"Много задач парсинга в обработке: {parsing_processing} (возможно зависшие воркеры)")

            if completed > chunking_new + 10:
                problems.append(f"Чанкинг отстает: {completed} завершенных vs {chunking_new} задач чанкинга")

            total_parsing = parsing_new + parsing_processing
            if abs(queued_ids - total_parsing) > 5:
                problems.append(f"Несоответствие ID: {queued_ids} в сете vs {total_parsing} задач парсинга")

            if parsing_new == 0 and parsing_processing == 0 and completed == 0:
                problems.append("Система полностью простаивает")

            # Проверка на аномально большое количество обработанных файлов
            if processed_files > completed + 100:
                problems.append(f"Много обработанных файлов без завершенных задач: {processed_files} vs {completed}")

            # Проверка Redis
            info = self.redis_client.info()
            memory_mb = info.get("used_memory", 0) / (1024 * 1024)

            if memory_mb > 500:  # MB
                problems.append(f"Высокое потребление памяти: {memory_mb:.1f} MB")

            # Вывод результатов
            if problems:
                print("❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ:")
                for i, problem in enumerate(problems, 1):
                    print(f"   {i}. {problem}")
            else:
                print("✅ Проблем не обнаружено")

        except Exception as e:
            print(f"❌ Ошибка проверки: {e}")

    def export_all_data(self):
        """Экспорт всех данных."""

        print("\n💾 ЭКСПОРТ ВСЕХ ДАННЫХ")
        print("-" * 30)

        timestamp = int(time.time())
        export_file = f"redis_full_export_{timestamp}.json"

        try:
            all_data = {}

            # Экспорт всех ключей
            keys = self.redis_client.keys("*")
            for key in keys:
                key_str = key.decode() if isinstance(key, bytes) else str(key)
                key_type = self.redis_client.type(key).decode()

                if key_type == "list":
                    data = self.redis_client.lrange(key, 0, -1)
                    all_data[key_str] = {
                        "type": "list",
                        "length": len(data),
                        "data": [item.decode() if isinstance(item, bytes) else str(item) for item in data],
                    }
                elif key_type == "set":
                    data = self.redis_client.smembers(key)
                    all_data[key_str] = {
                        "type": "set",
                        "size": len(data),
                        "data": [item.decode() if isinstance(item, bytes) else str(item) for item in data],
                    }

            # Сохранение
            export_path = self.tools_dir / export_file
            with open(export_path, "w", encoding="utf-8") as f:
                json.dump(all_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Данные экспортированы: tools/{export_file}")
            print(f"📊 Экспортировано ключей: {len(all_data)}")

        except Exception as e:
            print(f"❌ Ошибка экспорта: {e}")

    def run(self):
        """Запуск панели управления."""

        try:
            # Проверка подключения к Redis
            self.redis_client.ping()
        except redis.ConnectionError:
            print("❌ ОШИБКА: Не удается подключиться к Redis")
            print(f"   Проверьте что Redis запущен на {settings.REDIS_URL}")
            return 1

        while True:
            try:
                choice = self.show_menu()

                if choice == "0":
                    print("\n👋 До свидания!")
                    break
                elif choice == "1":
                    self.instant_analysis()
                elif choice == "2":
                    self.real_time_monitor()
                elif choice == "3":
                    self.detailed_analysis()
                elif choice == "4":
                    self.cleanup_redis()
                elif choice == "5":
                    self.view_data_structure()
                elif choice == "6":
                    self.quick_stats()
                elif choice == "7":
                    self.check_problems()
                elif choice == "8":
                    self.export_all_data()
                else:
                    print("❌ Неверный выбор. Попробуйте снова.")

                # Пауза перед возвратом в меню
                if choice != "0":
                    input("\n📝 Нажмите Enter для возврата в меню...")

            except KeyboardInterrupt:
                print("\n\n🛑 Работа прервана пользователем")
                break
            except Exception as e:
                print(f"\n❌ Неожиданная ошибка: {e}")
                input("📝 Нажмите Enter для продолжения...")

        return 0


def main():
    """Основная функция."""

    parser = argparse.ArgumentParser(description="Комплексная панель управления Redis диагностикой")
    parser.parse_args()  # Парсим аргументы для валидации, но не используем

    dashboard = RedisDashboard()
    return dashboard.run()


if __name__ == "__main__":
    sys.exit(main())
