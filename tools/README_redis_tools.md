# 🔍 Redis Diagnostic Tools

Комплексный набор профессиональных инструментов для диагностики и мониторинга Redis в проекте books.

## 📋 Содержание

1. [Быстрый старт](#быстрый-старт)
2. [Инструменты](#инструменты)
3. [Примеры использования](#примеры-использования)
4. [Структура данных Redis](#структура-данных-redis)
5. [Устранение проблем](#устранение-проблем)

## 🚀 Быстрый старт

### Единая панель управления
```bash
python tools/redis_dashboard.py
```

### Моментальный анализ
```bash
python tools/redis_diagnostic.py
```

### Мониторинг в реальном времени
```bash
python tools/redis_monitor.py --interval 3 --duration 60
```

## 🛠️ Инструменты

### 1. `redis_dashboard.py` - Главная панель управления

**Назначение:** Единая точка доступа ко всем инструментам диагностики.

**Возможности:**
- ✅ Моментальный анализ состояния
- ✅ Мониторинг в реальном времени  
- ✅ Детальный анализ с экспортом JSON
- ✅ Очистка данных Redis
- ✅ Просмотр структуры данных
- ✅ Быстрая статистика
- ✅ Проверка проблем
- ✅ Экспорт всех данных

**Интерфейс:** Интерактивное меню в консоли.

### 2. `redis_diagnostic.py` - Моментальная диагностика

**Назначение:** Полный снимок состояния Redis на данный момент.

```bash
# Базовый анализ
python tools/redis_diagnostic.py

# Детальный анализ с экспортом
python tools/redis_diagnostic.py --detailed --export-json report.json
```

**Что анализирует:**
- 📊 Общая информация Redis (версия, память, производительность)
- 📋 Все очереди проекта с содержимым
- 🗂️ Все множества (sets) с образцами
- 📈 Статистика и метрики
- 🚨 Обнаружение проблем и аномалий
- 📝 Образцы данных для анализа

### 3. `redis_monitor.py` - Мониторинг в реальном времени

**Назначение:** Непрерывный мониторинг с отображением тенденций и изменений.

```bash
# Базовый мониторинг (бесконечный)
python tools/redis_monitor.py

# С настройками
python tools/redis_monitor.py --interval 5 --duration 300
```

**Возможности:**
- 🔄 Обновление в реальном времени
- 📈 Скорости обработки (задач/мин)
- 📊 Тенденции изменения данных
- 📉 Мини-графики в консоли
- 🚨 Проблемы в реальном времени
- 💻 Системные метрики Redis

**Параметры:**
- `--interval N` - интервал обновления (сек)
- `--duration N` - длительность мониторинга (сек)

## 📊 Структура данных Redis

### Очереди и множества Redis

> **📋 Полная документация**: См. [../doc/redis_queues.md](../doc/redis_queues.md) - единый источник истины по структуре всех очередей Redis, их API и использованию.

### Пример данных

**Задача в очереди:**
```json
{
  "source_type": 1,
  "source_id": 826233,
  "filename": "826233.zip"
}
```

**ID в множестве:**
```
"1:826233"
```

## 📈 Примеры использования

### Сценарий 1: Ежедневная проверка системы
```bash
# 1. Быстрая статистика
python tools/redis_dashboard.py
# Выбрать пункт 6 (Быстрая статистика)

# 2. Проверка проблем
# Выбрать пункт 7 (Проверка проблем)
```

### Сценарий 2: Отладка проблем с производительностью
```bash
# 1. Детальный снимок состояния
python tools/redis_diagnostic.py --detailed --export-json debug_report.json

# 2. Мониторинг в течение 10 минут
python tools/redis_monitor.py --interval 2 --duration 600
```

### Сценарий 3: Очистка тестовых данных
```bash
python tools/redis_dashboard.py
# Выбрать пункт 4 (Очистка данных Redis)
# Выбрать нужный тип очистки
```

### Сценарий 4: Анализ производительности
```bash
# Мониторинг с коротким интервалом
python tools/redis_monitor.py --interval 1
# Наблюдаем:
# - Скорость завершения задач
# - Накопление в очередях
# - Тенденции изменения
```

## 🚨 Устранение проблем

### Проблема: Задачи застряли в обработке

**Симптомы:**
- `processing_tasks > 10` длительное время
- Скорость завершения = 0

**Диагностика:**
```bash
python tools/redis_diagnostic.py
```

**Решение:**
1. Проверить логи воркеров
2. Перезапустить воркеры
3. Переместить застрявшие задачи обратно в `new`

### Проблема: RAG отстает от обработки

**Симптомы:**
- `completed_tasks >> rag_tasks`

**Диагностика:**
```bash
python tools/redis_monitor.py
# Наблюдать тенденции RAG задач
```

**Решение:**
1. Проверить RAG сервис
2. Увеличить количество RAG воркеров

### Проблема: Несоответствие ID в очередях

**Симптомы:**
- `queued_ids != (new_tasks + processing_tasks)`

**Диагностика:**
```bash
python tools/redis_dashboard.py
# Пункт 5: Просмотр структуры данных
```

**Решение:**
1. Запустить recovery скрипт
2. Пересинхронизировать кэши

### Проблема: Высокое потребление памяти Redis

**Симптомы:**
- Memory usage > 100 MB

**Диагностика:**
```bash
python tools/redis_diagnostic.py --export-json memory_analysis.json
# Анализ размеров ключей в JSON
```

**Решение:**
1. Очистить завершенные задачи
2. Архивировать старые данные
3. Оптимизировать структуру данных

## 🔧 Расширенное использование

### Автоматический мониторинг
```bash
# Запуск мониторинга в фоне с логированием
python tools/redis_monitor.py --interval 60 --duration 3600 > redis_monitor.log 2>&1 &
```

### Регулярные отчеты
```bash
# Создание ежедневного отчета
python tools/redis_diagnostic.py --export-json "reports/redis_$(date +%Y%m%d).json"
```

### Интеграция с мониторингом
```python
# Использование в Python скриптах
from tools.redis_diagnostic import RedisAnalyzer

analyzer = RedisAnalyzer()
report = analyzer.analyze_all()

# Проверка на проблемы
if report["problems"]:
    send_alert(report["problems"])
```

## 📝 Заметки по производительности

1. **Интервал мониторинга:** Для продакшена рекомендуется интервал >= 5 секунд
2. **Экспорт данных:** JSON отчеты могут быть большими при множестве задач
3. **История:** Монитор хранит последние 50 измерений в памяти
4. **Подключения:** Все инструменты создают отдельные подключения к Redis

## 🎯 Лучшие практики

1. **Ежедневно:** Запускайте быструю проверку состояния
2. **При проблемах:** Используйте детальный анализ + мониторинг
3. **Перед релизом:** Экспортируйте полный снимок состояния  
4. **Очистка:** Регулярно очищайте завершенные задачи
5. **Мониторинг:** При отладке используйте короткие интервалы (1-2 сек)

---

**Автор:** Redis Diagnostic Tools Suite  
**Версия:** 1.0  
**Дата:** 2025-06-17 