# Инструмент восстановления зависших статусов книг

## Назначение

Обнаруживает и исправляет книги, которые "зависли" между фазами двухфазного сохранения:
- Метаданные сохранены в БД (`process_status = 10`)
- JSON-артефакт не создан на диске

Такая ситуация может возникнуть при сбое воркера после сохранения в БД, но до создания артефакта.

## Режимы работы

### Анализ (--dry-run)
```bash
python tools/recovery_incomplete_status.py --dry-run
```
- Только проверка без изменений
- Показывает количество проблемных книг
- Безопасен для продакшена

### Восстановление (--fix)
```bash
python tools/recovery_incomplete_status.py --fix
```
- Сбрасывает статус проблемных книг на `0` (новая запись)
- Позволяет воркеру переобработать их заново
- Требует осторожности в продакшене

## Принцип работы

1. **Поиск:** Ищет книги со статусом `process_status = 10`
2. **Проверка:** Проверяет существование соответствующего JSON-артефакта
3. **Анализ:** Если артефакт отсутствует - книга "зависла"
4. **Исправление:** (режим --fix) Сбрасывает статус на `0` для повторной обработки

## Интеграция с архитектурой

Инструмент дополняет двухфазную схему сохранения:

**Нормальный поток:**
1. `process_status = 10` (метаданные сохранены)
2. Создание JSON-артефакта
3. `process_status = 20` (полностью обработано)

**Восстановление после сбоя:**
1. Обнаружение зависших статусов (`1` без артефакта)
2. Сброс на `process_status = 0`
3. Повторная обработка воркером

## Мониторинг

Добавить в регулярные проверки системы:
```bash
# Контроль зависших статусов
python tools/recovery_incomplete_status.py --dry-run

# Общая статистика статусов
psql -c "SELECT process_status, COUNT(*) FROM books GROUP BY process_status;"
```

## Безопасность

- **--dry-run:** Полностью безопасен, не изменяет данные
- **--fix:** Изменяет статусы в БД, требует осторожности
- Не удаляет данные, только меняет статусы для повторной обработки
- Работает только с books в статусе `1`, не затрагивает полностью обработанные (`2`) 