#!/usr/bin/env python3
"""
Полный интеграционный тест пайплайна обработки книг, МАКСИМАЛЬНО приближенный к реальному воркеру.

ЭМУЛИРУЕТ РЕАЛЬНЫЙ ВОРКЕР (НОВАЯ АРХИТЕКТУРА):
- Создает task_data ТОЧНО как scanner_inventorizer.py
- Использует StorageManager для чтения файлов без перемещения
- Проверяет дубликаты через is_source_processed()
- Запускает весь пайплайн через BookProcessor.process()

ИСПОЛЬЗУЕТ ВСЕ РЕАЛЬНЫЕ КОМПОНЕНТЫ:
- BookProcessor со всей бизнес-логикой
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> → FB2Parser → FB2Transformer → CanonicalBook
- HashComputer, FragmentDetector, DateExtractor
- BookDataBuilder → BookDTO
- Проверка дубликатов через PostgreSQL

МОКАЮТСЯ ТОЛЬКО ОПЕРАЦИИ ЗАПИСИ:
- DatabaseSaver.save_book() (PostgreSQL INSERT)
- TaskQueueManager.enqueue_rag_task() (Redis LPUSH)
- save_artifact() (файловая запись)

РЕЗУЛЬТАТ: максимально честная диагностика с минимальными моками.

Сценарий отладки для одного файла, имитирующий пайплайн обработки книги.

- Находит файл по имени в исходных директориях.
- Создает task_data с полными метаданными архива.
- Вызывает BookProcessor.process() для выполнения полной логики обработки.
- НЕ изменяет исходный файл, НЕ пишет в БД, НЕ меняет очереди Redis.
- Выводит итоговую каноническую модель в консоль.
"""

import argparse
import json
import logging
import sys
import time
from pathlib import Path
from typing import Any
from unittest.mock import patch

# Добавление корневой директории проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.processing.book_processor import BookProcessor
from app.storage.local import LocalStorageManager


def decode_uuid7_timestamp(uuid_str: str) -> str:
    """Декодирует timestamp из UUID v7 обратно в дату.

    Согласно диаграмме uuid_extensions.uuid7:
    t1: 32 бита основной части Unix timestamp секунд (биты 127-96)
    t2/t3: unixts(4) | frac_secs(12) | ver(4) | frac_secs(12) (биты 95-64)

    Всего: 36 битов секунд + 24 бита долей секунд
    """
    import uuid
    from datetime import datetime, timezone

    try:
        u = uuid.UUID(uuid_str)

        # t1: основные 32 бита секунд (биты 127-96)
        t1_seconds = (u.int >> 96) & ((1 << 32) - 1)

        # t2/t3: следующие 32 бита (биты 95-64)
        t2_t3 = (u.int >> 64) & ((1 << 32) - 1)

        # Разбираем t2/t3: unixts(4) | frac_secs(12) | ver(4) | frac_secs(12)
        remaining_seconds = (t2_t3 >> 28) & 0xF  # 4 бита секунд (биты 31-28)
        frac_high = (t2_t3 >> 16) & 0xFFF  # 12 битов долей (биты 27-16)
        # version = (t2_t3 >> 12) & 0xF  # 4 бита версии (биты 15-12) - не используется
        frac_low = t2_t3 & 0xFFF  # 12 битов долей (биты 11-0)

        # Собираем полный 36-битный timestamp секунд
        unix_seconds = (t1_seconds << 4) | remaining_seconds

        # Собираем 24-битные доли секунд
        fractional_seconds = (frac_high << 12) | frac_low

        # Конвертируем доли секунд в десятичное значение (24 бита = 2^24 уровней)
        fractional_part = fractional_seconds / (1 << 24)

        # Создаем полный timestamp
        timestamp = unix_seconds + fractional_part

        # Преобразуем в datetime
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)

        return str(dt)

    except Exception as e:
        return f"Ошибка декодирования: {e}"


def create_realistic_task_data(file_path: str) -> dict[str, Any]:
    """Создает ТОЧНО такой же task_data как реальный сканер системы."""
    import time
    from pathlib import Path

    from app import settings

    # Используем РЕАЛЬНЫЕ системные функции для извлечения данных
    from app.utils import extract_source_id

    file_path_obj = Path(file_path)

    # Извлекаем source_id используя системную функцию
    source_id = extract_source_id(file_path_obj)
    if source_id is None:
        raise ValueError(f"Не удалось извлечь source_id из файла: {file_path}")

    # Определяем source_type по директории файла (как в реальном сканере)
    source_type = None
    for dir_path in settings.SOURCE_DIRS:
        if dir_path.is_dir() and str(file_path_obj).startswith(str(dir_path)):
            source_name = dir_path.name
            source_type = settings.SOURCE_TYPE_MAP.get(source_name)
            break

    if source_type is None:
        # Fallback: используем первый доступный source_type
        source_type = list(settings.SOURCE_TYPE_MAP.values())[0] if settings.SOURCE_TYPE_MAP else 1

    # НОВЫЙ формат задач для pipeline_20
    task_data = {
        "source_type": source_type,
        "source_id": source_id,
        "archive_path": str(file_path_obj),  # Полный путь к архиву
        "book_filename": file_path_obj.name,  # Имя файла книги в архиве (для ZIP это имя архива)
        "archive_mtime": file_path_obj.stat().st_mtime,  # Время модификации архива
    }

    # Добавляем поля которые устанавливает TaskQueueManager.claim_task()
    task_data["_claimed_at"] = time.time()

    return task_data


def format_timestamps_in_data(data: dict[str, Any]) -> dict[str, Any]:
    """Форматирует Unix timestamps в читаемый вид для полей времени."""
    from datetime import datetime

    # Создаем копию данных для безопасного изменения
    formatted_data = data.copy()

    # Форматируем _claimed_at если есть
    if "_claimed_at" in formatted_data and isinstance(formatted_data["_claimed_at"], (int, float)):
        dt = datetime.fromtimestamp(formatted_data["_claimed_at"])
        formatted_data["_claimed_at"] = dt.strftime("%Y-%m-%d %H:%M:%S")

    # Форматируем generated_at если есть
    if "generated_at" in formatted_data and isinstance(formatted_data["generated_at"], (int, float)):
        dt = datetime.fromtimestamp(formatted_data["generated_at"])
        formatted_data["generated_at"] = dt.strftime("%Y-%m-%d %H:%M:%S")

    return formatted_data


def save_diagnostic_report(diagnostic_data: dict[str, Any], output_file: Path):
    """Сохраняет диагностический отчет в JSON файл с форматированием времени."""

    # Добавляем timestamp в test_info секцию
    diagnostic_data["test_info"]["generated_at"] = time.time()

    # Форматируем временные поля в test_info
    if "test_info" in diagnostic_data:
        diagnostic_data["test_info"] = format_timestamps_in_data(diagnostic_data["test_info"])

        # Форматируем task_data внутри test_info
        if "task_data" in diagnostic_data["test_info"]:
            diagnostic_data["test_info"]["task_data"] = format_timestamps_in_data(
                diagnostic_data["test_info"]["task_data"]
            )

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(diagnostic_data, f, indent=2, ensure_ascii=False, default=str)


def run_test_with_mocks(input_path: Path, task_data: dict[str, Any]) -> dict[str, Any]:
    """Запускает тест с правильными моками."""

    # Диагностические данные - 3 ЧЕТКИЕ СЕКЦИИ
    diagnostic_data: dict[str, Any] = {
        # 1. СЛУЖЕБНАЯ ИНФОРМАЦИЯ (только необходимое)
        "test_info": {
            "version": "2.0",
            "test_type": "integration_test_with_proper_mocks",
            "stages": [],
            "errors": [],
            "timing": {},
        },
        # 2. ЧТО ПОПАДЕТ В БАЗУ ДАННЫХ
        "database_payload": {},
        # 3. ПОЛНОЕ СОДЕРЖИМОЕ JSON ФАЙЛА КНИГИ
        "book_artifact_full": {},
    }

    start_time = time.time()

    # Функции-моки
    def mock_save_book(self, book_dto, book_id: str):
        """Мок для DatabaseSaver.save_book_metadata_only() - собирает ТОЧНУЮ копию данных для БД"""
        # СЕКЦИЯ 2: ЧТО ПОПАДЕТ В БАЗУ ДАННЫХ (полная структура DTO)
        diagnostic_data["database_payload"] = {
            "book_id": book_id,
            "title": book_dto.title,
            "authors": book_dto.authors,  # Полный список авторов
            "lang": book_dto.lang,
            "series": book_dto.series,
            "series_number": book_dto.series_number,
            "genres": book_dto.genres,  # Полный список жанров
            "annotation": book_dto.annotation,  # ПОЛНАЯ аннотация (без обрезки)
            "metadata_hash": book_dto.metadata_hash,
            "file_format": book_dto.file_format,
            "keywords": book_dto.keywords,
            "raw_metadata": book_dto.raw_metadata,
        }

        diagnostic_data["test_info"]["stages"].append("✅ [MOCK] База данных - все метаданные собраны")
        return book_id  # Возвращаем book_id как оригинальный метод

    def mock_update_book_status(self, book_id: str, process_status: int):
        """Мок для DatabaseSaver.update_book_status() - избегаем транзакционных проблем"""
        diagnostic_data["test_info"]["stages"].append(f"✅ [MOCK] Статус книги {book_id} обновлен на {process_status}")
        return None  # Оригинальный метод ничего не возвращает

    def mock_is_source_processed(source_type: int, source_id: int) -> bool:
        """Мок для is_source_processed() - избегаем реальных запросов к БД"""
        diagnostic_data["test_info"]["stages"].append(
            f"✅ [MOCK] Проверка дубликатов для source_type={source_type}, source_id={source_id}"
        )
        return False  # Всегда возвращаем False (дубликатов нет)

    def mock_save_artifact(canonical_book, book_id: str):
        """Мок для save_artifact() - собирает ПОЛНОЕ содержимое JSON файла книги"""
        # СЕКЦИЯ 3: ПОЛНОЕ СОДЕРЖИМОЕ ФАЙЛА (как сохраняется в JSON.zst)
        diagnostic_data["artifact_content"] = {
            "id": book_id,
            "book_id_info": {
                "book_id": book_id,
                "generation_date": canonical_book.book_id_generation_date,
                "date_source": canonical_book.book_id_date_source,
                "decoded_date_from_uuid": decode_uuid7_timestamp(book_id),
            },
            "source": {
                "format": canonical_book.source_format,
                "file_size": getattr(canonical_book, "file_size", 0),  # Fallback если атрибута нет
                "source_type": canonical_book.source_type,
                "source_id": canonical_book.source_id,
            },
            "metadata": {
                "title": canonical_book.title,
                "lang": canonical_book.lang,
                "authors": [
                    {
                        "first_name": a.first_name,
                        "last_name": a.last_name,
                        "middle_name": a.middle_name,
                        "nickname": a.nickname,
                    }
                    for a in canonical_book.authors
                ],
                "translators": [
                    {
                        "first_name": t.first_name,
                        "last_name": t.last_name,
                        "middle_name": t.middle_name,
                        "nickname": t.nickname,
                    }
                    for t in canonical_book.translators
                ],
                "publication_date": str(canonical_book.publication_date),
                "genres": canonical_book.genres,
                "series": [{"name": s.name, "number": s.number} for s in canonical_book.sequences],
                "keywords": canonical_book.keywords,
                "annotation_md": canonical_book.annotation_md,  # ПОЛНАЯ аннотация в Markdown
            },
            "chapters": [
                {
                    "title": c.title,
                    "content_md": c.content_md,  # ПОЛНОЕ содержимое каждой главы
                }
                for c in canonical_book.chapters
            ],
            # Дополнительная диагностика
            "_diagnostic": {
                "book_id": book_id,
                "chapters_count": len(canonical_book.chapters),
                "total_content_size": f"{sum(len(c.content_md) for c in canonical_book.chapters):,} characters".replace(
                    ",", " "
                ),  # Общее количество символов в markdown-контенте всех глав
                "annotation_size": (len(canonical_book.annotation_md) if canonical_book.annotation_md else 0),
                "has_raw_metadata": bool(getattr(canonical_book, "raw_metadata", None)),
                "source_format": canonical_book.source_format,
            },
        }

        diagnostic_data["test_info"]["stages"].append("✅ [MOCK] JSON артефакт - полное содержимое собрано")

        # Возвращаем fake path как в оригинале
        return Path(f"/fake/artifacts/{book_id}.json.zst")

    def mock_enqueue_rag_task(self, book_id: str):
        """Мок для TaskQueueManager.enqueue_rag_task()"""
        diagnostic_data["test_info"]["stages"].append("✅ [MOCK] RAG задача - поставлена в очередь")
        return None  # Оригинальный метод ничего не возвращает

    # Запуск теста с моками
    try:
        # Добавляем основную информацию в служебную секцию
        diagnostic_data["test_info"]["input_file"] = str(input_path)
        diagnostic_data["test_info"]["task_data"] = task_data
        diagnostic_data["test_info"]["stages"].append("🚀 Начало обработки файла")

        # ЭМУЛИРУЕМ РЕАЛЬНЫЙ ВОРКЕР: восстанавливаем полный путь (новая архитектура)
        from app.utils import get_source_dir_by_type

        # Восстанавливаем исходный путь как это делает воркер
        source_type = task_data["source_type"]
        source_dir = get_source_dir_by_type(source_type)

        if source_dir is None:
            raise ValueError(f"Не найдена директория источника для source_type={source_type}")

        # В новой архитектуре файлы не перемещаются, обрабатываются на месте
        diagnostic_data["test_info"]["stages"].append("✅ [EMULATED] Файл готов к обработке")

        # Создаем процессор с LocalStorageManager
        storage_manager = LocalStorageManager()
        processor = BookProcessor(storage_manager)

        # Мокаем на уровне классов и модулей, а не экземпляров
        with (
            patch(
                "app.processing.database_saver.DatabaseSaver.save_book_metadata_only",
                new=mock_save_book,
            ),
            patch(
                "app.processing.database_saver.DatabaseSaver.update_book_status",
                new=mock_update_book_status,
            ),
            patch(
                "app.processing.book_processor.save_artifact",
                new=mock_save_artifact,
            ),
            patch(
                "app.processing.queue_manager.TaskQueueManager.enqueue_rag_task",
                new=mock_enqueue_rag_task,
            ),
            patch(
                "app.database.queries.is_source_processed",
                new=mock_is_source_processed,
            ),
        ):
            # ЭМУЛИРУЕМ проверку дубликатов как в реальном сканере (через мок)
            from app.database.queries import is_source_processed

            is_duplicate = is_source_processed(source_type, task_data["source_id"])
            if is_duplicate:
                diagnostic_data["test_info"]["stages"].append("⚠️ [CHECK] Обнаружен дубликат в БД (но продолжаем тест)")
            else:
                diagnostic_data["test_info"]["stages"].append("✅ [CHECK] Дубликатов не найдено")

            # Запускаем РЕАЛЬНЫЙ BookProcessor с новой сигнатурой
            result = processor.process(task_data)

            diagnostic_data["test_info"]["result"] = result
            diagnostic_data["test_info"]["stages"].append("✅ Обработка завершена успешно")
            diagnostic_data["test_info"]["stages"].append("✅ [EMULATED] Файл перемещен в processed")

    except Exception as e:
        # Логируем ошибку в диагностику
        error_info = {"type": type(e).__name__, "message": str(e), "stage": "process"}
        diagnostic_data["test_info"]["errors"].append(error_info)
        diagnostic_data["test_info"]["stages"].append(f"❌ Ошибка: {type(e).__name__}")

    finally:
        end_time = time.time()
        diagnostic_data["test_info"]["timing"]["total_seconds"] = round(end_time - start_time, 2)

    return diagnostic_data


def main():
    """Основная функция тестирования."""

    parser = argparse.ArgumentParser(description="Тест пайплайна обработки книг с правильными моками")
    parser.add_argument("--input-file", required=True, help="Путь к файлу книги для тестирования")
    parser.add_argument(
        "--output-file",
        default="tools/result_diagnostic_pipeline02_output.json",
        help="Файл для сохранения диагностики",
    )
    parser.add_argument(
        "--debug",
        "-debug",
        action="store_true",
        help="Показать детальные логи всех операций",
    )

    args = parser.parse_args()

    # Настройка логирования: без --debug только WARNING и выше
    if args.debug:
        log_level = logging.DEBUG
    else:
        log_level = logging.WARNING

    logging.basicConfig(level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    logger = logging.getLogger(__name__)

    try:
        input_path = Path(args.input_file)
        if not input_path.exists():
            logger.error(f"Файл не найден: {input_path}")
            return 1

        if args.debug:
            logger.info(f"🚀 Запуск теста пайплайна с файлом: {input_path}")
        else:
            print(f"🚀 Запуск теста пайплайна с файлом: {input_path.name}")

        # Подготовка РЕАЛИСТИЧНОГО task_data (как создает сканер)
        task_data = create_realistic_task_data(str(input_path))

        # Запуск теста с моками
        diagnostic_data = run_test_with_mocks(input_path, task_data)

        # Сохранение диагностического отчета
        output_path = Path(args.output_file)
        save_diagnostic_report(diagnostic_data, output_path)

        if args.debug:
            logger.info(f"📊 Диагностический отчет сохранен: {output_path}")
        else:
            print(f"📊 Диагностический отчет сохранен: {output_path.name}")

        # Выводим краткую сводку
        print("\n📋 КРАТКАЯ СВОДКА:")
        print(f"Этапов выполнено: {len(diagnostic_data['test_info']['stages'])}")
        print(f"Ошибок: {len(diagnostic_data['test_info']['errors'])}")
        print(f"Время выполнения: {diagnostic_data['test_info']['timing'].get('total_seconds', 'N/A')} сек")

        if diagnostic_data["test_info"]["errors"]:
            print("❌ ЕСТЬ ОШИБКИ!")
            return 1
        else:
            print("✅ ВСЕ ЭТАПЫ ПРОЙДЕНЫ УСПЕШНО!")

            # Детальная информация о моках только при --debug
            if args.debug:
                print("\n🔍 ПРОВЕРКА МОКОВ:")
                print("✅ save_artifact() - ЗАМОКАН (нет реальных файлов)")
                print("✅ DatabaseSaver.save_book() - ЗАМОКАН (нет записей в БД)")
                print("✅ enqueue_rag_task() - ЗАМОКАН (нет записей в Redis)")
            return 0

    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
