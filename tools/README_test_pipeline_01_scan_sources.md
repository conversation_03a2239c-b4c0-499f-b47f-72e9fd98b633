# Тест сканера источников (Pipeline 01)

## Описание

`run_02_test_pipeline_01.py` - полный интеграционный тест нового сканера источников `run_10_scan_sources.py`.

**ВАЖНО**: Документация требует обновления после внедрения Scanner-Inventorizer (2024).
**НОВАЯ АРХИТЕКТУРА**: Пакетная дедупликация, отказ от SET_PROCESSED, PostgreSQL как единственный источник правды.

## Что тестируется

### Полная логика сканера (УСТАРЕЛО - требует переписания под ScannerInventorizer)
- ⚠️ Сканирование через StorageManager (LocalStorageManager/S3StorageManager) 
- ⚠️ Извлечение source_id и создание BookCandidate объектов
- ⚠️ Пакетная проверка PostgreSQL book_sources (замена is_source_processed)
- ⚠️ Пакетная проверка Redis SET_QUEUED_IDS (замена is_task_already_queued)
- ⚠️ Атомарное добавление задач с архивными метаданными
- ⚠️ Новый формат задач: {source_type, source_id, archive_path, book_filename, archive_mtime}

### Тестовые сценарии (ТРЕБУЕТ ОБНОВЛЕНИЯ)
- **Новые архивы** - должны создавать задачи с архивными метаданными  
- **Дубликаты в PostgreSQL** - пакетная проверка book_sources
- **Активные задачи** - пакетная проверка SET_QUEUED_IDS
- **Некорректные архивы** - книги без source_id должны игнорироваться
- **~~Операции кэша~~** - SET_PROCESSED больше не используется

## Архитектура мокирования

### Что мокается (ТОЛЬКО операции записи)
```python
# Redis операции записи
- redis.lpush(QUEUE_NEW, task_json)
- redis.sadd(SET_QUEUED_IDS, task_key)
- redis.sadd(SET_PROCESSED, cache_key)

# PostgreSQL проверки
- is_source_processed(source_type, source_id)

# База данных для sync операций
- get_db_connection() с курсорами
```

### Что остается реальным (ВСЯ бизнес-логика)
```python
# Системные функции
- extract_source_id() - извлечение ID из имени файла
- pathlib операции сканирования директорий
- json.dumps() формирование задач

# Логика сканера  
- scan_and_register_new_files() - основная функция сканера
- Вся логика проверки дубликатов и очередей
- Подсчет статистики и логирование
```

## Использование

⚠️ **ВНИМАНИЕ**: Все примеры ниже устарели после внедрения Scanner-Inventorizer.
Тесты требуют полного переписания под новую архитектуру пакетной обработки.

### Базовый тест (режим ежедневного сканирования)
```bash
python tools/run_02_test_pipeline_01.py
```

**Что происходит:**
- Создается временная структура директорий источников
- Генерируются тестовые *.zip файлы разных категорий
- Запускается реальная функция `scan_and_register_new_files(use_cache=False)`
- Анализируются результаты мокированных Redis операций

### Тест с кэшем (режим обработки больших дампов)
```bash
python tools/run_02_test_pipeline_01.py --use-cache
```

**Дополнительно тестируется:**
- Предварительная синхронизация кэша `sync_redis_with_db()`
- Ускоренные проверки через SET_PROCESSED
- Fallback на PostgreSQL при недоступности кэша

### Тест только операций кэша
```bash
python tools/run_02_test_pipeline_01.py --test-cache-only
```

**Изолированно тестируется:**
- `sync_redis_with_db()` - синхронизация кэша с базой
- `clear_redis_cache()` - очистка кэша SET_PROCESSED

### Подробный вывод
```bash
python tools/run_02_test_pipeline_01.py --verbose
```

## Структура диагностического отчета

Результат сохраняется в `tools/result_diagnostic_pipeline01_scanner.json`:

```json
{
  "test_info": {
    "version": "1.0",
    "test_type": "scanner_integration_test",
    "use_cache": false,
    "stages": ["✅ Создана тестовая структура источников", "..."],
    "errors": [],
    "timing": {"total_seconds": 0.15},
    "temp_directory": "/tmp/tmpXXXXXX"
  },
  
  "scan_statistics": {
    "files_found": 12,
    "new_tasks": 5,
    "skipped_no_id": 2,
    "skipped_processed": 2,
    "skipped_queued": 2,
    "source_breakdown": {
      "zip_flibusta": {"total": 5, "new": 2, "processed_in_db": 1, "queued": 1, "invalid_id": 1},
      "zip_searchfloor": {"total": 4, "new": 1, "processed_in_db": 1, "queued": 0, "invalid_id": 1},
      "zip_anna": {"total": 3, "new": 2, "processed_in_db": 0, "queued": 1, "invalid_id": 0}
    }
  },
  
  "created_tasks": [
    {"source_type": 1, "source_id": 123456, "filename": "123456.zip"},
    {"source_type": 2, "source_id": 789012, "filename": "789012.zip"},
    // ... остальные новые задачи
  ],
  
  "redis_operations": {
    "queue_operations": [
      {"operation": "LPUSH", "queue": "books:queue:new", "count": 5}
    ],
    "set_operations": [
      {"operation": "SADD", "set": "books:set:queued_ids", "count": 5}
    ],
    "cache_operations": []
  }
}
```

## Интерпретация результатов

### Успешный тест
```
📋 КРАТКАЯ СВОДКА ТЕСТА СКАНЕРА:
Этапов выполнено: 6
Ошибок: 0
Файлов найдено: 12
Новых задач: 5
Пропущено дубликатов: 4
Время выполнения: 0.15 сек
✅ ВСЕ ЭТАПЫ ПРОЙДЕНЫ УСПЕШНО!

🔍 ПРОВЕРКА МОКОВ:
✅ Redis операции - ЗАМОКАНЫ (нет реальных записей)
✅ PostgreSQL проверки - ЗАМОКАНЫ (предустановленные ответы)  
✅ Файловая система - ИЗОЛИРОВАНА (временная директория)
```

### Ключевые метрики
- **files_found** - общее количество найденных *.zip файлов
- **new_tasks** - количество созданных задач (должно равняться файлам категории "new")
- **skipped_processed** - файлы пропущенные как дубликаты БД
- **skipped_queued** - файлы пропущенные как уже в очередях
- **skipped_no_id** - файлы с некорректными именами

### Проверка корректности
```python
# Ожидаемая формула баланса:
files_found == new_tasks + skipped_processed + skipped_queued + skipped_no_id

# Для тестовых данных:
12 == 5 + 2 + 2 + 2 ✓
```

## Преимущества подхода

### Максимальная достоверность
- Использует **реальную бизнес-логику** сканера без изменений
- Тестирует **реальные функции** extract_source_id, проверки дубликатов
- Эмулирует **реальную файловую структуру** источников

### Безопасность
- **Изоляция**: временные директории, моки записи
- **Воспроизводимость**: предустановленные тестовые сценарии
- **Контроль**: полная диагностика всех операций

### Полнота покрытия
- **Все режимы**: ежедневный, дамп, операции кэша
- **Все сценарии**: новые, дубликаты, очереди, ошибки
- **Вся статистика**: детализация по источникам

## Связь с реальной системой

### Прямое соответствие
```bash
# Реальный сканер
python run_10_scan_sources.py

# Тест аналогичной логики
python tools/run_02_test_pipeline_01.py
```

### Совместимость API
Тест использует **идентичные вызовы**:
- `scan_and_register_new_files(use_cache=False)` 
- `sync_redis_with_db(redis_client)`
- `clear_redis_cache(redis_client)`

### Валидация конфигурации
Тест проверяет корректность настроек:
- `settings.SOURCE_TYPE_MAP` - маппинг типов источников
- `settings.QUEUE_NEW` - имена очередей Redis
- `settings.SET_QUEUED_IDS` - имена множеств Redis

## Рекомендации по использованию

### Регулярное тестирование
```bash
# Перед деплоем изменений сканера
python tools/run_02_test_pipeline_01.py

# При изменении логики очередей
python tools/run_02_test_pipeline_01.py --use-cache
```

### Диагностика проблем
```bash
# При подозрении на проблемы с кэшем
python tools/run_02_test_pipeline_01.py --test-cache-only --verbose

# Полная диагностика с детальным логом
python tools/run_02_test_pipeline_01.py --verbose --output-file debug_scan.json
```

### Интеграция в CI/CD
```bash
# Автотест в пайплайне
python tools/run_02_test_pipeline_01.py --output-file ci_scan_results.json
if [ $? -eq 0 ]; then echo "Scanner test PASSED"; else echo "Scanner test FAILED"; exit 1; fi
``` 