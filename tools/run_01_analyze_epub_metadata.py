# run_01_analyze_epub_metadata.py
import io
import logging
import sys
import zipfile
from pathlib import Path

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Импорт утилит
from tools.utils import collect_zip_archives  # noqa: E402

# --- КОНФИГУРАЦИЯ ---

# 1. Укажите путь к директории, где лежат ваши ZIP-архивы с книгами.
#    Скрипт будет рекурсивно искать файлы во всех вложенных папках.
#    Пример для Windows: Path("D:/Project/books/zip/zip_flibusta")
#    Пример для Linux/Mac: Path("/home/<USER>/books/zip_flibusta")
SOURCE_DIR = Path("/mnt/d/Project/books/zip/zip_flibusta")

# 2. Установите лимит на количество обрабатываемых ZIP-архивов.
#    Это полезно для быстрого теста на небольшой выборке.
#    Установите 0 для обработки ВСЕХ найденных файлов.
FILE_LIMIT = 1000

# 3. Имя выходного файла для сохранения результатов анализа.
OUTPUT_FILE = "epub_metadata_analysis.md"

# --- КОНЕЦ КОНФИГУРАЦИИ ---


def setup_logging():
    """Настраивает логирование для вывода в консоль."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        stream=sys.stdout,
    )


def find_and_extract_opf_content(zip_path: Path) -> tuple[str, str] | None:
    """Обрабатывает один ZIP-архив. Находит внутри него EPUB,
    а затем извлекает содержимое .opf файла.

    EPUB - это тоже ZIP-архив, поэтому здесь логика "двойной распаковки".

    Args:
        zip_path: Путь к внешнему ZIP-архиву.

    Returns:
        Кортеж (имя_epub_файла, содержимое_opf) или None в случае неудачи.

    """
    try:
        with zipfile.ZipFile(zip_path, "r") as outer_zip:
            epub_file_name = None
            # 1. Ищем EPUB файл внутри внешнего ZIP-архива
            for name in outer_zip.namelist():
                if name.lower().endswith(".epub"):
                    epub_file_name = name
                    break

            if not epub_file_name:
                return None  # В этом архиве нет EPUB файлов

            # 2. Читаем содержимое EPUB файла в память (он сам является архивом)
            epub_content_bytes = outer_zip.read(epub_file_name)

            # 3. Открываем EPUB как вложенный ZIP-архив из памяти
            with zipfile.ZipFile(io.BytesIO(epub_content_bytes), "r") as inner_epub_zip:
                opf_file_path = None
                # 4. Ищем .opf файл внутри EPUB
                for name in inner_epub_zip.namelist():
                    if name.lower().endswith(".opf"):
                        opf_file_path = name
                        break

                if not opf_file_path:
                    logging.warning(f"Не найден .opf файл в {zip_path.name} -> {epub_file_name}")
                    return None

                # 5. Извлекаем и декодируем содержимое .opf файла
                opf_content = inner_epub_zip.read(opf_file_path).decode("utf-8", errors="ignore")
                return epub_file_name, opf_content

    except zipfile.BadZipFile:
        logging.error(f"Поврежденный ZIP-архив, пропускаем: {zip_path.name}")
        return None
    except Exception as e:
        logging.error(f"Неожиданная ошибка при обработке {zip_path.name}: {e}")
        return None


def main():
    """Основная функция скрипта."""
    setup_logging()
    log = logging.getLogger(__name__)

    if not SOURCE_DIR.is_dir():
        log.critical(f"ОШИБКА: Директория не найдена: {SOURCE_DIR}")
        log.critical("Пожалуйста, укажите правильный путь в переменной SOURCE_DIR вверху скрипта.")
        return

    log.info(f"Начинаем сканирование директории: {SOURCE_DIR}")
    all_zip_files = [Path(p) for p in collect_zip_archives([str(SOURCE_DIR)])]

    if not all_zip_files:
        log.warning("В указанной директории не найдено ни одного ZIP-архива.")
        return

    log.info(f"Найдено ZIP-архивов: {len(all_zip_files)}")

    if FILE_LIMIT > 0:
        files_to_process = all_zip_files[:FILE_LIMIT]
        log.info(f"Будет обработано файлов (согласно лимиту): {len(files_to_process)}")
    else:
        files_to_process = all_zip_files
        log.info("Будут обработаны ВСЕ найденные файлы.")

    processed_count = 0
    epub_found_count = 0

    # Открываем файл для дозаписи, чтобы не терять результаты при повторных запусках
    with open(OUTPUT_FILE, "a", encoding="utf-8") as f_out:
        log.info(f"Результаты будут сохранены в файл: {OUTPUT_FILE}")

        for i, zip_path in enumerate(files_to_process):
            result = find_and_extract_opf_content(zip_path)

            if result:
                epub_name, opf_content = result
                epub_found_count += 1

                # Записываем результат в файл
                f_out.write(f"// --- File: {zip_path.name} -> {epub_name} ---\n")
                f_out.write(opf_content.strip())
                f_out.write("\n\n")

            processed_count += 1

            if processed_count % 100 == 0:
                log.info(f"Обработано {processed_count}/{len(files_to_process)} архивов...")

            log.info(f"[{i}/{len(files_to_process)}] Обрабатываю архив: {Path(zip_path).name}")

    log.info("=" * 50)
    log.info("Анализ завершен.")
    log.info(f"Всего обработано ZIP-архивов: {processed_count}")
    log.info(f"Найдено и извлечено метаданных из EPUB: {epub_found_count}")
    log.info(f"Результат сохранен в: {OUTPUT_FILE}")
    log.info("=" * 50)


if __name__ == "__main__":
    main()
