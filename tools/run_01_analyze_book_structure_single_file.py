# tools/run_01_analyze_book_structure_single_file.py

"""
🚀 БЫСТРЫЙ ТЕСТ ПАРСЕРА СТРУКТУРЫ КНИГ

Упрощенная версия для тестирования на ОДНОМ конкретном ZIP архиве.
Использует реальную логику проекта для проверки работы парсинга.
Работает ТОЛЬКО с ОДНИМ ZIP архивом, содержащим FB2 файлы.
"""

import json
import logging
import sys
from pathlib import Path
from typing import Any

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Импортируем реальные компоненты проекта
from app.processing.canonical_model import CanonicalBook
from app.processing.fragment_detector import FragmentDetector
from app.processing.parser_dispatcher import ParserDispatcher

# Импортируем общие утилиты tools
from tools.utils import (
    get_canonical_book_from_file,
    process_zip_archive,
)

# =============================================================================
# НАСТРОЙКИ - УКАЖИТЕ ПУТЬ К КОНКРЕТНОМУ ZIP АРХИВУ
# =============================================================================
TEST_ARCHIVE = Path("/mnt/d/Project/books/zip/zip_flibusta/811206.zip")

# Лимит FB2 файлов из архива (для быстрого тестирования)
FB2_FILES_LIMIT = 3

# Имя выходного файла (создается в папке tools/)
OUTPUT_FILE = Path(__file__).parent / "result_analyze_books_structure_single_file.json"

# Используем РЕАЛЬНЫЙ детектор фрагментов из проекта
fragment_detector = FragmentDetector()


def setup_logging():
    """Настраивает подробное логирование."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        stream=sys.stdout,
    )


def analyze_book_structure(canonical_book: CanonicalBook) -> dict[str, Any]:
    """Анализирует структуру книги и возвращает основные метрики."""

    # Анализ глав - УЛУЧШЕННАЯ ВЕРСИЯ
    chapters_info: dict[str, Any] = {
        "count": len(canonical_book.chapters),
        "details": [],  # Детальная информация по каждой главе
    }

    # Добавляем детальную информацию о каждой главе
    details_list: list[dict[str, Any]] = []
    for i, chapter in enumerate(canonical_book.chapters, 1):
        chapter_length = len(chapter.content_md)
        details_list.append(
            {
                "chapter_number": i,
                "title": chapter.title,
                "content_length_chars": chapter_length,
                "content_length_kb": round(chapter_length / 1024, 2),
                "is_empty": chapter_length == 0,
                "is_very_short": chapter_length < 500,  # Меньше 500 символов
                "is_very_long": chapter_length > 100000,  # Больше 100к символов
            }
        )
    chapters_info["details"] = details_list

    # Статистика по главам
    if canonical_book.chapters:
        chapter_lengths = [len(ch.content_md) for ch in canonical_book.chapters]
        chapters_info["statistics"] = {
            "total_content_chars": sum(chapter_lengths),
            "total_content_kb": round(sum(chapter_lengths) / 1024, 2),
            "average_chapter_length": round(sum(chapter_lengths) / len(chapter_lengths)),
            "min_chapter_length": min(chapter_lengths),
            "max_chapter_length": max(chapter_lengths),
            "empty_chapters": sum(1 for length in chapter_lengths if length == 0),
            "very_short_chapters": sum(1 for length in chapter_lengths if 0 < length < 500),
            "very_long_chapters": sum(1 for length in chapter_lengths if length > 100000),
        }
    else:
        chapters_info["statistics"] = {}

    # Анализ серий
    sequences_info = {
        "count": len(canonical_book.sequences),
        "details": [
            {
                "name": seq.name,
                "number": seq.number,
                "has_number": seq.number is not None,
                "number_type": (type(seq.number).__name__ if seq.number is not None else None),
            }
            for seq in canonical_book.sequences
        ],
    }

    # Анализ авторов
    authors_info = {
        "count": len(canonical_book.authors),
        "details": [
            {
                "full_name": " ".join(filter(None, [a.first_name, a.middle_name, a.last_name])),
                "first_name": a.first_name,
                "middle_name": a.middle_name,
                "last_name": a.last_name,
                "nickname": a.nickname,
            }
            for a in canonical_book.authors
        ],
    }

    return {
        "title": canonical_book.title,
        "lang": canonical_book.lang,
        "source_format": canonical_book.source_format,
        # Основные метрики
        "chapters_count": len(canonical_book.chapters),
        "sequences_count": len(canonical_book.sequences),
        # Детальная информация
        "chapters": chapters_info,
        "sequences": sequences_info,
        "authors": authors_info,
        "metadata": {
            "genres": canonical_book.genres,
            "keywords": canonical_book.keywords,
            "publication_date": (
                canonical_book.publication_date.isoformat() if canonical_book.publication_date else None
            ),
            "has_annotation": bool(canonical_book.annotation_md.strip()),
            "annotation_length": len(canonical_book.annotation_md),
            "annotation_preview": (
                canonical_book.annotation_md[:200] + "..."
                if len(canonical_book.annotation_md) > 200
                else canonical_book.annotation_md
            ),
            "is_trial_fragment": fragment_detector.is_fragment(canonical_book),
        },
    }


def process_single_archive(archive_path, parser_dispatcher, fb2_limit=None):
    """Обрабатывает ОДИН ZIP архив и извлекает FB2 файлы для анализа"""
    processed_count = 0

    def fb2_processor(archive_path: str, fb2_filename: str, temp_fb2_path: Path):
        """Обработчик для отдельного FB2 файла"""
        nonlocal processed_count

        print(f"  📖 Обрабатываем: {fb2_filename}")

        # Парсим через общую утилиту (использует проектную логику)
        canonical_book = get_canonical_book_from_file(temp_fb2_path, parser_dispatcher)

        # Анализируем структуру
        analysis = analyze_book_structure(canonical_book)
        analysis["fb2_filename"] = fb2_filename

        processed_count += 1
        print(f"    ✅ Успешно: {canonical_book.title}")

        return analysis

    def fb2_filter(fb2_filename: str) -> bool:
        """Фильтр для ограничения количества обрабатываемых файлов"""
        return processed_count < (fb2_limit or float("inf"))

    def error_handler(archive_path: str, fb2_filename: str, error: Exception):
        """Обработчик ошибок"""
        if fb2_filename:
            print(f"    ❌ Ошибка при обработке {fb2_filename}: {error}")
        else:
            print(f"❌ Ошибка при обработке архива {archive_path}: {error}")

    # Используем общую утилиту для обработки ZIP архива
    results = process_zip_archive(
        zip_path=archive_path,
        fb2_processor=fb2_processor,
        fb2_filter=fb2_filter if fb2_limit else None,
        error_handler=error_handler,
    )

    if results:
        print(f"📦 В архиве найдено и обработано FB2 файлов: {len(results)}")
        if fb2_limit and len(results) >= fb2_limit:
            print(f"📦 Обработаны первые {len(results)} файлов (лимит: {fb2_limit})")

    return results


def main():
    """Основная функция быстрого теста."""
    setup_logging()
    logger = logging.getLogger(__name__)

    # Проверяем наличие тестового архива
    if not TEST_ARCHIVE.exists():
        logger.critical(f"❌ Тестовый архив не найден: {TEST_ARCHIVE}")
        logger.info("Укажите корректный путь к ZIP архиву в переменной TEST_ARCHIVE")
        sys.exit(1)

    logger.info(f"🔍 Тестируем парсер на архиве: {TEST_ARCHIVE}")

    # Инициализируем компоненты проекта
    parser_dispatcher = ParserDispatcher()

    try:
        # Обрабатываем архив
        logger.info("📦 Обрабатываем ZIP архив...")
        results = process_single_archive(TEST_ARCHIVE, parser_dispatcher, FB2_FILES_LIMIT)

        if not results:
            logger.error("❌ Ни одна книга не была успешно обработана")
            sys.exit(1)

        # Сохраняем результаты
        report = {
            "test_metadata": {
                "archive_path": str(TEST_ARCHIVE),
                "fb2_files_limit": FB2_FILES_LIMIT,
                "processed_books": len(results),
                "script_version": "1.0.0-single-archive",
            },
            "books_analysis": results,
        }

        with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"💾 Анализ сохранен в: {OUTPUT_FILE}")

        # Итоговая статистика
        logger.info("=" * 50)
        logger.info("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ")
        logger.info("=" * 50)
        logger.info(f"📦 Архив: {TEST_ARCHIVE.name}")
        logger.info(f"📚 Обработано книг: {len(results)}")

        if results:
            total_chapters = sum(book["chapters_count"] for book in results)
            total_sequences = sum(book["sequences_count"] for book in results)
            logger.info(f"📖 Всего глав: {total_chapters}")
            logger.info(f"📚 Всего серий: {total_sequences}")

            # Показываем детальную информацию по каждой книге
            logger.info("\n📖 ДЕТАЛЬНЫЙ АНАЛИЗ КНИГ:")
            for book_idx, book in enumerate(results, 1):
                logger.info(f"\n📖 КНИГА {book_idx}: {book['title']}")
                logger.info(
                    f"   👥 Авторы: {', '.join([author['full_name'] for author in book['authors']['details']])}"
                )
                logger.info(f"   🌍 Язык: {book['lang']}")

                # Информация о главах
                chapters = book["chapters"]
                if chapters["count"] > 0:
                    stats = chapters["statistics"]
                    logger.info(f"   📊 Глав: {chapters['count']} (общий объем: {stats['total_content_kb']} КБ)")
                    logger.info(f"   📊 Средний размер главы: {stats['average_chapter_length']:,} символов")

                    # Показываем проблемные главы
                    if stats["empty_chapters"] > 0:
                        logger.info(f"   ⚠️  Пустых глав: {stats['empty_chapters']}")
                    if stats["very_short_chapters"] > 0:
                        logger.info(f"   ⚠️  Очень коротких глав (<500 символов): {stats['very_short_chapters']}")
                    if stats["very_long_chapters"] > 0:
                        logger.info(f"   ⚠️  Очень длинных глав (>100к символов): {stats['very_long_chapters']}")

                    # Показываем первые несколько глав
                    logger.info("   📄 Первые главы:")
                    for chapter in chapters["details"][:5]:  # Первые 5 глав
                        title = chapter["title"] if chapter["title"] else f"Глава {chapter['chapter_number']}"
                        size_info = f"{chapter['content_length_kb']} КБ"

                        warning = ""
                        if chapter["is_empty"]:
                            warning = " [ПУСТАЯ]"
                        elif chapter["is_very_short"]:
                            warning = " [КОРОТКАЯ]"
                        elif chapter["is_very_long"]:
                            warning = " [ДЛИННАЯ]"

                        logger.info(f"      {chapter['chapter_number']}. {title} ({size_info}){warning}")

                    if chapters["count"] > 5:
                        logger.info(f"      ... и еще {chapters['count'] - 5} глав")
                else:
                    logger.info("   📊 Главы: НЕ НАЙДЕНЫ")

                # Информация о сериях
                sequences = book["sequences"]
                if sequences["count"] > 0:
                    logger.info(f"   📚 Серии: {sequences['count']}")
                    for seq in sequences["details"]:
                        number_info = f" #{seq['number']}" if seq["has_number"] else ""
                        logger.info(f"      - {seq['name']}{number_info}")
                else:
                    logger.info("   📚 Серии: НЕ НАЙДЕНЫ")

        logger.info("🎉 Тест завершен успешно!")

    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}")
        import traceback

        logger.error(f"Трассировка:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
