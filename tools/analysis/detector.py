"""Детектор аномалий в книгах с поддержкой системы карантина."""

from typing import Any

from app.processing.anthology_detector import AnthologyDetector
from app.processing.canonical_model import CanonicalBook
from app.processing.error_handler import QuarantineType
from app.processing.fragment_detector import FragmentDetector
from app.processing.parsers.fb2.fb2_transformer import FB2Transformer
from app.processing.small_book_detector import SmallBookDetector
from tools.analysis.registry import AnomalyPathRegistry

# Пороги для определения аномалий
ANOMALY_THRESHOLDS: dict[str, Any] = {
    # Устаревшие пороги для глав (сохраняем для совместимости)
    "low_chapters": 5,
    "suspicious_range": (80, 100),
    "high_chapters": 100,
    # Новые пороги для детекторов карантина (используют переменные окружения)
    # Эти значения будут переопределены в детекторах через env переменные
    "small_book_min_chapters": 4,
    "small_book_min_content": 10000,
    "anthology_min_chapters": 8,
    "anthology_author_threshold": 0.3,
}


class AnomalyDetector:
    """Детектор аномалий в книгах с поддержкой системы карантина."""

    def __init__(self, anomaly_registry: AnomalyPathRegistry = None):
        self.thresholds = ANOMALY_THRESHOLDS
        self.anomaly_registry = anomaly_registry

        # Инициализация детекторов карантина
        self.fragment_detector = FragmentDetector()
        self.small_book_detector = SmallBookDetector()
        self.anthology_detector = AnthologyDetector()

    def detect_anomaly_types(
        self,
        canonical_book: CanonicalBook,
        archive_path: str,
        fb2_filename: str,
        fb2_transformer: FB2Transformer = None,
    ) -> list[str]:
        """Определяет типы аномалий в книге, учитывая исключения."""
        # Проверяем, не исключен ли файл из детекции
        if self.anomaly_registry and self.anomaly_registry.is_excluded(archive_path, fb2_filename):
            return []  # Файл исключен - аномалий нет

        anomalies = []

        # === НОВЫЕ ДЕТЕКТОРЫ КАРАНТИНА (ПРИОРИТЕТ) ===

        # 1. Проверка на ознакомительные фрагменты
        if self.fragment_detector.is_fragment(canonical_book):
            anomalies.append("trial_fragments")

        # 2. Проверка на маленькие книги (более точная логика чем low_chapters)
        quarantine_type = self.small_book_detector.check_book_structure(canonical_book)
        if quarantine_type:
            anomalies.append("small_books")

        # 3. Проверка на антологии/сборники
        if self.anthology_detector.is_anthology(canonical_book):
            anomalies.append("anthology_books")

        # === УСТАРЕВШИЕ ДЕТЕКТОРЫ (ДЛЯ СОВМЕСТИМОСТИ) ===

        chapters_count = len(canonical_book.chapters)

        # Проверка аномалий глав (устаревшая логика)
        chapter_anomaly = self._check_chapters_anomaly(chapters_count)
        if chapter_anomaly:
            anomalies.append(chapter_anomaly)

        # Проверка сломанных сносок (если доступен трансформер)
        if fb2_transformer and self._check_footnotes_anomaly(fb2_transformer):
            anomalies.append("broken_footnotes")

        return anomalies

    def detect_anomaly_types_with_reasons(
        self,
        canonical_book: CanonicalBook,
        archive_path: str,
        fb2_filename: str,
        fb2_transformer: FB2Transformer = None,
    ) -> dict[str, str]:
        """Определяет типы аномалий в книге с причинами срабатывания.

        Returns:
            Словарь {тип_аномалии: причина_срабатывания}
        """
        # Проверяем, не исключен ли файл из детекции
        if self.anomaly_registry and self.anomaly_registry.is_excluded(archive_path, fb2_filename):
            return {}  # Файл исключен - аномалий нет

        anomalies_with_reasons = {}

        # === НОВЫЕ ДЕТЕКТОРЫ КАРАНТИНА (ПРИОРИТЕТ) ===

        # 1. Проверка на ознакомительные фрагменты
        if self.fragment_detector.is_fragment(canonical_book):
            reason = self.fragment_detector.get_fragment_reason(canonical_book)
            anomalies_with_reasons["trial_fragments"] = reason

        # 2. Проверка на структурные аномалии книг (детализированная логика)
        quarantine_type = self.small_book_detector.check_book_structure(canonical_book)
        if quarantine_type:
            reason = self.small_book_detector.get_rejection_reason(canonical_book)
            # Регистрируем конкретный тип аномалии вместо обобщенного "small_books"
            if quarantine_type == QuarantineType.SMALL_CONTENT:
                anomalies_with_reasons["small_content"] = reason
            elif quarantine_type == QuarantineType.FEW_CHAPTERS:
                anomalies_with_reasons["few_chapters"] = reason
            else:
                # Fallback для совместимости с возможными новыми типами
                anomalies_with_reasons["small_books"] = reason

        # 3. Проверка на антологии/сборники
        if self.anthology_detector.is_anthology(canonical_book):
            reason = self.anthology_detector.get_anthology_reason(canonical_book)
            anomalies_with_reasons["anthology_books"] = reason

        # === УСТАРЕВШИЕ ДЕТЕКТОРЫ (ДЛЯ СОВМЕСТИМОСТИ) ===

        chapters_count = len(canonical_book.chapters)

        # Проверка аномалий глав (устаревшая логика)
        chapter_anomaly = self._check_chapters_anomaly(chapters_count)
        if chapter_anomaly:
            reason = f"Количество глав: {chapters_count} (пороги: {self.thresholds})"
            anomalies_with_reasons[chapter_anomaly] = reason

        # Проверка сломанных сносок (если доступен трансформер)
        if fb2_transformer and self._check_footnotes_anomaly(fb2_transformer):
            anomalies_with_reasons["broken_footnotes"] = "Обнаружены сломанные сноски в тексте"

        return anomalies_with_reasons

    def _check_chapters_anomaly(self, chapters_count: int) -> str | None:
        """Проверяет аномалии количества глав."""
        if chapters_count < self.thresholds["low_chapters"]:
            return "low_chapters"
        elif chapters_count > self.thresholds["high_chapters"]:
            return "high_chapters"
        elif self.thresholds["suspicious_range"][0] <= chapters_count <= self.thresholds["suspicious_range"][1]:
            return "suspicious_chapters"

        return None

    def _check_footnotes_anomaly(self, fb2_transformer: FB2Transformer) -> bool:
        """Проверяет наличие сломанных сносок."""
        if hasattr(fb2_transformer, "has_broken_footnotes"):
            return fb2_transformer.has_broken_footnotes()
        return False
