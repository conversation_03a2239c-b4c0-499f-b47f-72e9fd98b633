"""Определяет режим работы анализатора."""

import json
from pathlib import Path


class AnalysisMode:
    """Определяет режим работы анализатора."""

    @staticmethod
    def determine_mode(registry_path: Path, force_full: bool = False) -> str:
        """Определяет режим работы на основе наличия аномалий в реестре."""
        if force_full:
            return "full_analysis"

        if not registry_path.exists():
            return "full_analysis"

        try:
            with open(registry_path, "r", encoding="utf-8") as f:
                registry = json.load(f)

            # Проверяем, есть ли реальные аномалии в реестре
            anomalies = registry.get("anomalies", {})
            total_anomalies = sum(len(paths) for paths in anomalies.values())

            # Если нет аномалий - делаем полный анализ
            return "anomaly_rescan" if total_anomalies > 0 else "full_analysis"

        except (json.JSONDecodeError, KeyError):
            print("⚠️  Поврежденный JSON реестр, переходим к полному анализу")
            return "full_analysis"
