"""Общие типы данных для системы анализа аномалий."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, TypedDict


class AnomalyRegistryData(TypedDict):
    """Типизированная структура для реестра аномалий."""

    metadata: dict[str, Any]
    anomalies: dict[str, list[str]]
    excluded_files: list[str]  # Файлы исключенные из детекции аномалий


@dataclass
class ProcessingStats:
    """Статистика обработки с правильной типизацией."""

    processed_archives: int = 0
    processed_books: int = 0
    error_count: int = 0
    anomalies_found: int = 0
    anomalies_fixed: int = 0
    # === Новая статистика для режима ресканирования ===
    fully_fixed_files: int = 0
    partially_fixed_files: int = 0
    new_anomalies_in_rescan: int = 0
    start_time: datetime = field(default_factory=datetime.now)
    end_time: datetime | None = None
    total_time_seconds: float = 0.0
    books_per_second: float = 0.0
