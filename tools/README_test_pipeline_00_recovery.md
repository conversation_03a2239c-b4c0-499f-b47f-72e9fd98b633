# Тест системы восстановления run_02_test_pipeline_01.py

## Описание

Полный интеграционный тест для скрипта восстановления `run_00_recovery.py`. Тестирует все этапы восстановления системы после критических сбоев.

## Что тестируется

### 5 этапов восстановления:
- **0️⃣ Восстановление SET активных задач** (`rebuild_queued_set`)
- **1️⃣ Восстановление зависших задач** (`TaskMonitor.check_and_recover_stale_tasks`)
- **2️⃣ Сверка файлов в /in_progress/ с БД** (`_recover_orphaned_files`)
- **3️⃣ Очистка поврежденных записей Redis** (`_cleanup_redis`)
- **4️⃣ Проверка целостности данных** (`check_data_integrity`)

### Реальные компоненты (НЕ мокаются):
- `SystemRecovery` с полной бизнес-логикой
- `TaskMonitor`, `TaskQueueManager`, `FileManager`
- Все функции работы с файлами и путями
- Централизованные функции проверки (`utils`, `database.queries`)

### Мокаются только операции I/O:
- **Redis** - через `fakeredis` (полная эмуляция)
- **PostgreSQL** - через `unittest.mock`
- **Файловые операции** - временные директории

## Тестовые сценарии

### Симулируемые сбои:
1. **Зависшие задачи** в `QUEUE_PROCESSING` (старше `WORKER_TIMEOUT`)
2. **Поврежденные задачи** (невалидный JSON)
3. **Потерянные файлы** в директориях `in_progress`
4. **Несоответствие** файлов и БД
5. **Несоответствие** `SET_QUEUED_IDS` и очередей

### Создаваемые данные:
- 2 источника данных (source_1, source_2)
- 3 зависшие задачи (2 нормальные + 1 поврежденная)
- 2 потерянных файла (1 в БД + 1 новый)
- 1 активная задача (НЕ зависшая)
- Завершенные и новые задачи для `rebuild_queued_set`

## Использование

### Базовый запуск:
```bash
python tools/run_02_test_pipeline_01.py
```

### С подробным выводом:
```bash
python tools/run_02_test_pipeline_01.py --verbose
```

### Кастомный файл отчета:
```bash
python tools/run_02_test_pipeline_01.py --output-file my_recovery_test.json
```

## Пример вывода

```
📋 КРАТКАЯ СВОДКА ТЕСТА ВОССТАНОВЛЕНИЯ:
Этапов выполнено: 14
Ошибок: 0
Время выполнения: 0.04 сек

🔧 РЕЗУЛЬТАТЫ ВОССТАНОВЛЕНИЯ:
0️⃣ SET активных задач: ✅ (размер: 5)
1️⃣ Зависшие задачи: восстановлено=2, в карантин=0, ошибок=1
2️⃣ Потерянные файлы: найдено=2, восстановлено=2, ошибок=0
3️⃣ Redis очистка: поврежденных=0, очищено=0, ошибок=0
4️⃣ Целостность данных: книг=1250, потерянных источников=3

📊 СОСТОЯНИЕ REDIS:
   ДО  | ПОСЛЕ
Новые:          1 |   3
Обработка:      4 |   1
Завершенные:    1 |   1
SET активных:   5 |   5

✅ ВСЕ ЭТАПЫ ВОССТАНОВЛЕНИЯ ПРОЙДЕНЫ УСПЕШНО!
```

## Диагностический отчет

Сохраняется в JSON формате с полной информацией:

### Структура отчета:
- **test_info** - служебная информация о тесте
- **recovery_results** - результаты каждого этапа восстановления  
- **redis_state_before/after** - состояние Redis до и после

### Пример анализа:
```json
{
  "recovery_results": {
    "set_rebuild": {"success": true, "final_set_size": 5},
    "stale_tasks": {"recovered": 2, "quarantined": 0, "errors": 1},
    "orphaned_files": {"found_orphans": 2, "recovered": 2, "errors": 0}
  }
}
```

## Интеграция в CI/CD

Тест возвращает правильные exit коды:
- **0** - все этапы восстановления пройдены успешно
- **1** - есть ошибки в процессе восстановления

### Пример использования в GitHub Actions:
```yaml
- name: Test recovery system
  run: python tools/run_02_test_pipeline_01.py
```

## Зависимости

Добавить в `requirements-dev.txt`:
```
fakeredis  # Для интеграционных тестов
```

Установка:
```bash
pip install fakeredis
```

## Польза для разработки

1. **Быстрая проверка** всех функций восстановления
2. **Детальная диагностика** каждого этапа
3. **Безопасное тестирование** (не затрагивает реальные данные)
4. **Регрессионные тесты** при изменении логики восстановления
5. **Документация поведения** системы при сбоях

## Отличия от production

- **Redis** эмулируется через `fakeredis`
- **PostgreSQL** запросы мокаются  
- **Файлы** создаются во временных директориях
- **Логика восстановления** работает на 100% как в реальной системе

Это обеспечивает максимально точное тестирование при полной безопасности. 