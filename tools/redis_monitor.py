#!/usr/bin/env python3
"""
Профессиональный монитор Redis в реальном времени.

Показывает изменения и тенденции:
- Динамика очередей
- Скорость обработки
- Проблемы в реальном времени
- Графики активности

Использование:
python tools/redis_monitor.py [--interval 5] [--duration 60]
"""

import argparse
import sys
import time
from collections import deque
from datetime import datetime
from pathlib import Path
from typing import Any

import redis

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import settings


class RedisMonitor:
    """Монитор Redis в реальном времени."""

    def __init__(self, interval: int = 5, max_history: int = 50):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.interval = interval
        self.max_history = max_history

        # История метрик (deque для ограниченного размера)
        self.history: dict[str, deque[Any]] = {
            "timestamp": deque(maxlen=max_history),
            "new_tasks": deque(maxlen=max_history),
            "processing_tasks": deque(maxlen=max_history),
            "completed_tasks": deque(maxlen=max_history),
            "rag_tasks": deque(maxlen=max_history),
            "queued_ids": deque(maxlen=max_history),
            "processed_files": deque(maxlen=max_history),
            "memory_usage": deque(maxlen=max_history),
            "commands_per_sec": deque(maxlen=max_history),
        }

        self.previous_stats: dict[str, Any] = {}
        self.start_time = time.time()

    def collect_metrics(self) -> dict:
        """Собирает текущие метрики."""

        try:
            # Основные метрики очередей
            parsing_new = self.redis_client.llen(settings.QUEUE_PARSING_NEW)
            parsing_processing = self.redis_client.llen(settings.QUEUE_PARSING_PROCESSING)
            completed_tasks = self.redis_client.llen(settings.QUEUE_COMPLETED)
            chunking_new = self.redis_client.llen(settings.QUEUE_CHUNKING_NEW)
            queued_ids = self.redis_client.scard(settings.SET_QUEUED_IDS)
            processed_files = 0  # SET_PROCESSED упразднен в новой архитектуре

            # Информация о Redis
            info = self.redis_client.info()
            memory_mb = info.get("used_memory", 0) / (1024 * 1024)
            commands_per_sec = info.get("instantaneous_ops_per_sec", 0)

            return {
                "timestamp": time.time(),
                "new_tasks": parsing_new,
                "processing_tasks": parsing_processing,
                "completed_tasks": completed_tasks,
                "rag_tasks": chunking_new,
                "queued_ids": queued_ids,
                "processed_files": processed_files,
                "memory_usage": memory_mb,
                "commands_per_sec": commands_per_sec,
                "total_tasks": parsing_new + parsing_processing,
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
            }

        except Exception as e:
            print(f"❌ Ошибка сбора метрик: {e}")
            return {}

    def update_history(self, metrics: dict):
        """Обновляет историю метрик."""

        for key in self.history:
            if key in metrics:
                self.history[key].append(metrics[key])

    def calculate_changes(self, current: dict) -> dict:
        """Рассчитывает изменения относительно предыдущего измерения."""

        changes = {}

        if self.previous_stats:
            for key in [
                "new_tasks",
                "processing_tasks",
                "completed_tasks",
                "rag_tasks",
                "queued_ids",
                "processed_files",
            ]:
                if key in current and key in self.previous_stats:
                    changes[key] = current[key] - self.previous_stats[key]
                else:
                    changes[key] = 0
        else:
            # Первое измерение
            for key in [
                "new_tasks",
                "processing_tasks",
                "completed_tasks",
                "rag_tasks",
                "queued_ids",
                "processed_files",
            ]:
                changes[key] = 0

        return changes

    def calculate_rates(self) -> dict:
        """Рассчитывает скорости обработки."""

        rates = {}

        if len(self.history["timestamp"]) >= 2:
            time_window = self.history["timestamp"][-1] - self.history["timestamp"][0]

            if time_window > 0:
                # Скорость завершения задач (задач/мин)
                completed_change = self.history["completed_tasks"][-1] - self.history["completed_tasks"][0]
                rates["completion_rate"] = (completed_change / time_window) * 60

                # Скорость обработки (задач/мин)
                processed_change = self.history["processed_files"][-1] - self.history["processed_files"][0]
                rates["processing_rate"] = (processed_change / time_window) * 60

                # Скорость генерации RAG задач
                rag_change = self.history["rag_tasks"][-1] - self.history["rag_tasks"][0]
                rates["rag_generation_rate"] = (rag_change / time_window) * 60
            else:
                rates = {
                    "completion_rate": 0,
                    "processing_rate": 0,
                    "rag_generation_rate": 0,
                }
        else:
            rates = {
                "completion_rate": 0,
                "processing_rate": 0,
                "rag_generation_rate": 0,
            }

        return rates

    def detect_trends(self) -> dict:
        """Обнаруживает тенденции в данных."""

        trends = {}

        if len(self.history["new_tasks"]) >= 3:
            # Тенденция новых задач
            recent = list(self.history["new_tasks"])[-3:]
            if recent[2] > recent[1] > recent[0]:
                trends["new_tasks"] = "РАСТЕТ"
            elif recent[2] < recent[1] < recent[0]:
                trends["new_tasks"] = "ПАДАЕТ"
            else:
                trends["new_tasks"] = "СТАБИЛЬНО"

            # Тенденция задач в обработке
            recent = list(self.history["processing_tasks"])[-3:]
            if all(x > 0 for x in recent) and recent[2] > recent[0]:
                trends["processing"] = "НАКАПЛИВАЕТСЯ"
            elif all(x == 0 for x in recent):
                trends["processing"] = "ПУСТО"
            else:
                trends["processing"] = "НОРМАЛЬНО"
        else:
            trends = {"new_tasks": "НЕТ ДАННЫХ", "processing": "НЕТ ДАННЫХ"}

        return trends

    def display_dashboard(self, metrics: dict, changes: dict, rates: dict, trends: dict):
        """Отображает dashboard в консоли."""

        # Очистка экрана (работает в большинстве терминалов)
        print("\033[2J\033[H", end="")

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        runtime = time.time() - self.start_time

        print("🔍 REDIS MONITOR - РЕАЛЬНОЕ ВРЕМЯ")
        print("=" * 70)
        print(f"⏰ Время: {current_time} | ⏱️  Работает: {runtime:.0f}с | 🔄 Интервал: {self.interval}с")
        print()

        # Основные метрики
        print("📊 ОСНОВНЫЕ МЕТРИКИ:")
        print(
            f"   🆕 Новые задачи:      {metrics.get('new_tasks', 0):4d} ({self._format_change(changes.get('new_tasks', 0))})"
        )
        print(
            f"   ⚙️  В обработке:      {metrics.get('processing_tasks', 0):4d} ({self._format_change(changes.get('processing_tasks', 0))})"
        )
        print(
            f"   ✅ Завершенные:      {metrics.get('completed_tasks', 0):4d} ({self._format_change(changes.get('completed_tasks', 0))})"
        )
        print(
            f"   🤖 RAG задачи:       {metrics.get('rag_tasks', 0):4d} ({self._format_change(changes.get('rag_tasks', 0))})"
        )
        print(
            f"   🆔 ID в очередях:    {metrics.get('queued_ids', 0):4d} ({self._format_change(changes.get('queued_ids', 0))})"
        )
        print(
            f"   📝 Legacy кэш обработанных: {metrics.get('processed_files', 0):4d} ("
            f"{self._format_change(changes.get('processed_files', 0))})"
        )
        print()

        # Скорости
        print("📈 СКОРОСТИ (задач/мин):")
        print(f"   ✅ Завершение:       {rates.get('completion_rate', 0):6.1f}")
        print(f"   📝 Обработка:        {rates.get('processing_rate', 0):6.1f}")
        print(f"   🤖 Генерация RAG:    {rates.get('rag_generation_rate', 0):6.1f}")
        print()

        # Тенденции
        print("📊 ТЕНДЕНЦИИ:")
        print(f"   🆕 Новые задачи:     {trends.get('new_tasks', 'НЕТ ДАННЫХ')}")
        print(f"   ⚙️  Обработка:       {trends.get('processing', 'НЕТ ДАННЫХ')}")
        print()

        # Система
        print("💻 СИСТЕМА:")
        print(f"   🧠 Память Redis:     {metrics.get('memory_usage', 0):5.1f} MB")
        print(f"   ⚡ Команд/сек:       {metrics.get('commands_per_sec', 0):4d}")
        print(f"   👥 Клиентов:         {metrics.get('connected_clients', 0):4d}")
        print(f"   📦 Версия Redis:     {metrics.get('redis_version', 'N/A')}")
        print()

        # Мини-график (последние 10 измерений)
        if len(self.history["new_tasks"]) >= 2:
            print("📈 ГРАФИК НОВЫХ ЗАДАЧ (последние 10 измерений):")
            self._draw_mini_chart(list(self.history["new_tasks"])[-10:], "🆕")
            print()

        # Проблемы
        problems = self._detect_realtime_problems(metrics, rates)
        if problems:
            print("🚨 ПРОБЛЕМЫ:")
            for problem in problems:
                print(f"   ⚠️  {problem}")
        else:
            print("✅ ПРОБЛЕМ НЕ ОБНАРУЖЕНО")

        print()
        print("💡 Нажмите Ctrl+C для остановки")

    def _format_change(self, change: int) -> str:
        """Форматирует изменение значения."""
        if change > 0:
            return f"↗️+{change}"
        elif change < 0:
            return f"↘️{change}"
        else:
            return "→0"

    def _draw_mini_chart(self, values: list, symbol: str):
        """Рисует мини-график в консоли."""
        if not values or len(values) < 2:
            print("   Недостаточно данных")
            return

        max_val = max(values) if max(values) > 0 else 1

        # Нормализуем значения к высоте 5 символов
        height = 5
        normalized = [int((v / max_val) * height) for v in values]

        # Рисуем график по строкам (сверху вниз)
        for row in range(height, 0, -1):
            line = "   "
            for val in normalized:
                if val >= row:
                    line += symbol  # Используем переданный символ
                else:
                    line += " "
            # Показываем значение для этого уровня
            level_value = int((row / height) * max_val)
            line += f" {level_value:3d}"
            print(line)

        # Подписи по оси X (индексы)
        x_line = "   "
        for i in range(len(values)):
            x_line += str(i % 10)
        print(x_line)

        # Значения
        val_line = "   "
        for val in values:
            val_line += f"{val % 10}"
        print(val_line)

    def _detect_realtime_problems(self, metrics: dict, rates: dict) -> list:
        """Обнаруживает проблемы в реальном времени."""
        problems = []

        # Застой в обработке
        if metrics.get("processing_tasks", 0) > 5 and rates.get("completion_rate", 0) == 0:
            problems.append("Задачи застряли в обработке")

        # Дисбаланс очередей
        completed = metrics.get("completed_tasks", 0)
        rag = metrics.get("rag_tasks", 0)
        if completed > rag + 5:
            problems.append(f"RAG отстает: {completed} завершенных vs {rag} RAG")

        # Высокая нагрузка
        if metrics.get("memory_usage", 0) > 100:  # MB
            problems.append("Высокое потребление памяти Redis")

        # Отсутствие активности
        if all(v == 0 for v in [metrics.get("new_tasks", 0), metrics.get("processing_tasks", 0)]):
            problems.append("Система простаивает")

        return problems

    def monitor(self, duration: int = None):
        """Запускает мониторинг."""

        print("🚀 Запуск Redis Monitor...")
        print(f"📊 Интервал: {self.interval}с")
        if duration:
            print(f"⏱️  Длительность: {duration}с")
        print("💡 Нажмите Ctrl+C для остановки\n")

        end_time = time.time() + duration if duration else None

        try:
            while True:
                if end_time and time.time() > end_time:
                    break

                # Собираем метрики
                metrics = self.collect_metrics()
                if not metrics:
                    time.sleep(self.interval)
                    continue

                # Рассчитываем изменения
                changes = self.calculate_changes(metrics)

                # Обновляем историю
                self.update_history(metrics)

                # Рассчитываем скорости и тенденции
                rates = self.calculate_rates()
                trends = self.detect_trends()

                # Отображаем dashboard
                self.display_dashboard(metrics, changes, rates, trends)

                # Сохраняем текущие метрики
                self.previous_stats = metrics.copy()

                # Ждем до следующего измерения
                time.sleep(self.interval)

        except KeyboardInterrupt:
            print("\n\n🛑 Мониторинг остановлен пользователем")
        except Exception as e:
            print(f"\n\n❌ Ошибка мониторинга: {e}")


def main():
    """Основная функция."""

    parser = argparse.ArgumentParser(description="Мониторинг Redis в реальном времени")
    parser.add_argument(
        "--interval",
        type=int,
        default=5,
        help="Интервал обновления в секундах (по умолчанию: 5)",
    )
    parser.add_argument(
        "--duration",
        type=int,
        help="Длительность мониторинга в секундах (по умолчанию: бесконечно)",
    )

    args = parser.parse_args()

    try:
        monitor = RedisMonitor(interval=args.interval)
        monitor.monitor(duration=args.duration)

    except redis.ConnectionError:
        print("❌ ОШИБКА: Не удается подключиться к Redis")
        print(f"   Проверьте что Redis запущен на {settings.REDIS_URL}")
        return 1
    except Exception as e:
        print(f"❌ КРИТИЧЕСКАЯ ОШИБКА: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
