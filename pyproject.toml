# pyproject.toml

# Конфигурация для Ruff (форматтер + линтер)
[tool.ruff]
line-length = 120
src = ["app"]  # "utils"

[tool.ruff.lint]
# Фокус на критически важных правилах: ошибки, неиспользуемый код, импорты
select = [
    "E4", "E7", "E9",  # критические синтаксические ошибки
    "F",               # pyflakes - неиспользуемые импорты, переменные
    "W6",              # критические предупреждения
    "I",               # isort - сортировка импортов
    "B",               # bugbear - распространенные баги
    "C90",             # McCabe сложность
    "UP006",           # Dict -> dict
]
# Игнорируем проблемы существующего кода
ignore = [
    "F403", "F405",    # star imports разрешены в этом проекте
    "E501",            # длинные строки - пусть форматтер решает
    "C901",            # сложность функций - требует рефакторинга, но не блокирует
    "S101",            # assert в коде
    "B008",            # вызовы функций в дефолтных аргументах
]

[tool.ruff.format]
# Настройки, аналогичные Black
quote-style = "double"

# Конфигурация для Mypy (анализатор типов)
[tool.mypy]
python_version = "3.12"
ignore_missing_imports = true
# Прагматичные настройки для существующего кода
warn_return_any = false              # пока не требуем строгую типизацию Any
disallow_untyped_defs = false        # не требуем аннотации везде
warn_unused_ignores = true           # предупреждаем о лишних # type: ignore
warn_redundant_casts = true          # предупреждаем о лишних кастах
# Фокус на критических проблемах типизации
disallow_untyped_calls = false       # пока разрешаем вызовы без типов
check_untyped_defs = true            # но проверяем тела функций без аннотаций
no_implicit_optional = false         # не разрешаем неявные опциональные типы

