# Обработка данных: извлечение, валидация, трансформация

## Архитектура пайплайна

Система обрабатывает книги через многоэтапный пайплайн:
1. **Извлечение** - сканирование источников, парсинг архивов
2. **Валидация** - проверка дубликатов, фрагментов, структуры
3. **Трансформация** - приведение к канонической модели
4. **Сохранение** - запись в PostgreSQL и очередь RAG

## Извлечение данных

### Сканирование источников

**Компонент:** `app/ingestion/scanner_inventorizer.py` (заменил устаревший scanner.py)

```python
class ScannerInventorizer:
    def scan_all_sources(self) -> ScanStats
```

**Алгоритм:**
- Пакетное сканирование архивов через StorageManager
- Извлечение списка книг из каждого архива
- Пакетная дедупликация через Redis SET_QUEUED_IDS + PostgreSQL
- Создание задач с полными метаданными архива (source_type, source_id, archive_path, book_filename, archive_mtime)

**Оптимизация производительности:**
- Redis кэш `SET_PROCESSED` для быстрой проверки дубликатов (O(1))
- Batch-операции для больших объемов (10k+ записей)
- Синхронизация с PostgreSQL через серверные курсоры

### Извлечение из архивов

**Компонент:** `app/processing/archive_processor.py`

```python
def extract_archive(archive_path: Path) -> Path
def find_book_files(extracted_dir: Path) -> list[Path]
```

**Безопасность:**
- Валидация структуры ZIP (проверка путей `../`, `/`)
- Ограничение количества файлов
- Поиск основного файла книги по приоритету расширений

**Поддерживаемые форматы:**
- `.fb2` (приоритет 1)
- `.epub` (приоритет 2) 
- `.mobi`, `.azw`, `.azw3` (приоритет 3)

### Парсинг файлов

**Компонент:** `app/processing/parser_dispatcher.py`

```python
def parse_to_canonical(file_path: Path) -> CanonicalBook
```

**Определение формата:**
1. Проверка расширения файла
2. Анализ магических байтов (fallback)
3. Выбор соответствующего парсера

**Текущая поддержка:**
- **FB2**: полная реализация (`FB2Parser` + `FB2Transformer`)
- **EPUB/MOBI**: заглушки для будущего развития

## Валидация данных

### Проверка дубликатов

**Компонент:** `app/database/queries.py`

```python
def is_source_processed(source_type: int, source_id: int) -> bool
def check_book_duplicates(metadata_hash: str) -> dict[str, str] | None
```

**Алгоритм дедупликации:**
- Уникальное ограничение `(source_type, source_id)` в `book_sources`
- Проверка `metadata_hash` для обнаружения идентичных произведений
- Приоритет PostgreSQL как единственного источника правды

### Детекция фрагментов

**Компонент:** `app/processing/fragment_detector.py`

```python
def is_fragment(canonical_book: CanonicalBook) -> bool
```

**Критерии фрагментов (FragmentDetector):**
- Ключевые слова в тексте ("ознакомительный", "фрагмент")

> Структурный анализ (количество глав, общий объём) выполняется отдельным компонентом `SmallBookDetector`.

### Валидация хэшей

**Компонент:** `app/processing/hash_computer.py`

```python
def compute_hashes(canonical_book: CanonicalBook) -> dict[str, str]
```

**Компоненты metadata_hash:**
1. Нормализованное название книги
2. Авторы (сортированные по иерархии: last_name → first_name → middle_name)
3. Переводчики (аналогичная сортировка)
4. Приоритетная серия (с номером > 0, иначе первая)

**Нормализация:** удаление пунктуации, приведение к нижнему регистру, MD5 UUID

## Трансформация данных

### Преобразование в каноническую модель

**Компонент:** `app/processing/parsers/fb2/fb2_transformer.py`

```python
def transform(fb2_book: FB2Book, publication_date: date) -> CanonicalBook
```

**Этапы трансформации:**
1. Извлечение метаданных (авторы, жанры, серии)
2. Преобразование содержимого в Markdown
3. Интеллектуальное разбиение на главы
4. Фильтрация служебных разделов

### Система приоритетных эвристик для глав

**Проблема:** 12% FB2 файлов имеют некорректную разметку глав

**Решение:** Каскадная система эвристик по приоритету:

1. **Рекурсивный алгоритм секций** - `<section><title>`
2. **Подзаголовки** - `<subtitle>`  
3. **Жирные заголовки** - `<p><strong>Глава N</strong></p>`
4. **Простые заголовки** - `<p>Глава N</p>`
5. **Курсивные заголовки** - `<p><emphasis>Глава N</emphasis></p>`
6. **Разделители** - `***`

**Пороговый контроль:** если эвристика находит ≥4 глав, используется её результат

### Очистка и нормализация

**Обработка текста:**
- Преобразование FB2 элементов в Markdown
- Удаление мягких переносов и артефактов
- Нормализация пробелов и знаков препинания
- Обработка сносок и ссылок

**Обработка метаданных:**
- Нормализация имен авторов
- Стандартизация жанров
- Извлечение ключевых слов
- Валидация дат публикации

## Сохранение результатов

### Двухфазное сохранение

**Компонент:** `app/processing/database_saver.py`

```python
def save_book_metadata_only(book_dto: BookDTO, book_id: str) -> str
def update_book_status(book_id: str, process_status: int) -> None
```

**Фазы:**
1. **Фаза 1**: сохранение метаданных с `process_status=10`
2. **Фаза 2**: создание артефакта, обновление статуса на `process_status=20`

**Преимущества:** атомарность операций, восстановление после сбоев

### Структуры данных

**DTO модель:** `app/processing/dto.py`
```python
@dataclass
class BookDTO:
    title: str
    lang: str
    authors: list[dict[str, Any]]
    metadata_hash: str
    # ... опциональные поля
```

**Каноническая модель:** `app/processing/canonical_model.py`
```python
@dataclass  
class CanonicalBook:
    title: str
    lang: str
    authors: list[CanonicalAuthor]
    chapters: list[CanonicalChapter]
    # ... метаданные и содержимое
```

## Контроль качества

### Метрики обработки

- **Пропускная способность**: количество книг/час
- **Коэффициент дедупликации**: % отсеянных дубликатов  
- **Коэффициент карантина**: % файлов в карантине
- **Качество данных**: % успешно обработанных файлов

### Обработка ошибок

**Типы ошибок:**
- `QuarantineError` - бизнес-ошибки (фрагменты, некорректные данные)
- `ProcessingError` - временные ошибки (retry логика)
- `FatalError` - критические системные ошибки

**Стратегии восстановления:**
- Автоматический retry для временных сбоев
- Карантин для проблемных файлов с детальным логированием
- Graceful degradation при недоступности внешних сервисов 