# Соглашения именования

## Обзор

Проект использует стандартные Python соглашения (PEP 8) с некоторыми специфичными дополнениями для архитектуры обработки книг.

## Файлы и модули

### Основные модули
**Формат**: `snake_case.py`

**Примеры**:
- `queue_manager.py` - управление очередями Redis
- `database_saver.py` - сохранение в PostgreSQL

### Исполняемые скрипты
**Формат**: `run_[номер]_[описание].py`

**Примеры**:
- `run_10_scan_sources.py` - сканирование источников
- `run_20_process_book_worker.py` - воркер обработки книг

## Классы

**Формат**: `PascalCase`

**Примеры**:
- `TaskQueueManager` - управление задачами
- `CanonicalBook` - каноническая модель книги

### Исключения
**Формат**: `PascalCase` + суффикс `Error`

**Примеры**:
- `ProcessingError` - базовое исключение обработки
- `QuarantineError` - исключение для карантина

## Функции и методы

**Формат**: `snake_case`

**Примеры**:
- `is_source_processed()` - проверка обработки источника
- `handle_error()` - обработка ошибок

### Публичные методы
Все методы используют `snake_case` без префиксов:

**Примеры**:
- `claim_task()` - захват задачи из очереди
- `move_to_quarantine()` - перемещение в карантин

### Приватные методы
**Формат**: `_snake_case` (один подчерк)

**Примеры**:
- `_handle_quarantine_error()` - внутренняя обработка ошибок карантина
- `_analyze_unknown_error()` - анализ неизвестных ошибок

## Переменные

### Локальные переменные
**Формат**: `snake_case`

**Примеры**:
- `task_data` - данные задачи
- `retry_count` - счетчик попыток

### Атрибуты экземпляра
**Формат**: `snake_case`

**Примеры**:
- `self.logger` - логгер класса
- `self.redis_client` - клиент Redis
- `self.max_retries` - максимальное количество повторов

### Параметры функций
**Формат**: `snake_case`

**Примеры**:
- `source_type: int` - тип источника
- `task_data: dict[str, Any]` - данные задачи

## Константы

**Формат**: `UPPER_SNAKE_CASE`

### Настройки конфигурации
**Примеры**:
- `REDIS_URL` - URL подключения к Redis
- `DATABASE_URL` - строка подключения к БД

### Имена очередей и ключей Redis

> **📋 Полная документация**: См. [../doc/redis_queues.md](../doc/redis_queues.md) - единый источник истины по всем именам очередей Redis и их назначению.

### Константы обработки
**Примеры**:
- `WORKER_TIMEOUT` - таймаут воркера
- `ZSTD_COMPRESSION_LEVEL` - уровень сжатия

## Перечисления (Enums)

### Класс перечисления
**Формат**: `PascalCase`

**Примеры**:
- `ErrorType` - типы ошибок

### Значения перечисления
**Формат**: `UPPER_CASE`

**Примеры**:
```python
class ErrorType(Enum):
    QUARANTINE = "quarantine"
    RETRY = "retry"
    FATAL = "fatal"
```

## Типы данных

### Базовые типы
**Формат**: `PascalCase`

**Примеры**:
- `CanonicalAuthor` - модель автора
- `CanonicalSequence` - модель серии

### Составные типы (Union)
**Формат**: `PascalCase` + описательное имя

**Примеры**:
- `ParagraphContent` - содержимое параграфа
- `AnnotationElement` - элемент аннотации

### Типы аннотаций
Используются стандартные типы из `typing`:

**Примеры**:
- `Optional[str]` - опциональная строка
- `dict[str, Any]` - словарь с произвольными значениями

## Поля моделей данных

### Dataclass поля
**Формат**: `snake_case`

**Примеры**:
```python
@dataclass
class CanonicalBook:
    source_id: Optional[int]
    source_type: Optional[int]
    publication_date: Optional[date]
    content_md: str
```

### JSON ключи
Следуют соглашениям полей - `snake_case`:

**Примеры**:
- `"source_type"` - тип источника в JSON
- `"file_path"` - путь к файлу в задаче

## Логирование

### Сообщения логов
Используют эмодзи для категоризации:

**Примеры**:
- `"📚 Обработано книг: {count}"` - информация
- `"❌ Ошибка обработки: {error}"` - ошибка
- `"🚫 Файл помещен в карантин"` - предупреждение

### Контекстные переменные
Используют `snake_case` и описательные имена:

**Примеры**:
- `file_path` - путь к файлу
- `error_message` - сообщение об ошибке

## Директории и пути

### Структура данных
Используют `snake_case`:

**Примеры**:
- `in_progress/` - файлы в обработке
- `processed/` - обработанные файлы
- `quarantine/` - файлы в карантине

### Переменные путей
**Примеры**:
- `source_dir` - директория источника
- `canonical_storage_path` - путь к канонической модели

## Чанки книги

### Идентификатор чанка
Используется иерархическая композиция `book_id + chapter_number + chunk_number_in_chapter`.
Это обеспечивает устойчивость к изменению алгоритма чанкинга и облегчает отладку.

**Пример payload в Qdrant:**
```json
{
  "book_id": "06775d70-0000-7000-8000-0938792691f1",
  "chapter_number": 5,
  "chunk_number_in_chapter": 12
}
```

### Пути к файлам чанков
путь к хранилищу чанков в .env в пременной CHUNKS_STORAGE_PATH
Файлы хранятся по иерархическому пути, который восстанавливается из payload без доп. запросов:

`/06/77/5d/06775d70-0000-7000-8000-0938792691f1/chapter_5-chunk_12.json.zst` 