# Бизнес-логика системы обработки электронных книг

## Общая бизнес-идея

Система представляет собой высокопроизводительный пайплайн для автоматизированной обработки больших коллекций электронных книг. Основная цель — преобразование разрозненных архивов с книгами из множественных источников в структурированную базу знаний, оптимизированную для использования в RAG (Retrieval-Augmented Generation) системах и поисковых решениях.

## Целевые пользователи

**Основные пользователи:**
- Разработчики RAG-систем и ИИ-приложений, работающих с литературным контентом
- Создатели поисковых систем для электронных библиотек
- Исследователи в области обработки естественного языка и текстовой аналитики

**Вторичные пользователи:**
- Администраторы электронных библиотек
- Разработчики рекомендательных систем для книг
- Аналитики литературного контента

## Решаемые проблемы

### 1. Проблема масштаба
- **Проблема**: Ручная обработка коллекций из сотен тысяч книг невозможна
- **Решение**: Полностью автоматизированный пайплайн с горизонтальным масштабированием через воркеры

### 2. Проблема дедупликации
- **Проблема**: Одни и те же книги присутствуют в разных источниках с различными метаданными
- **Решение**: Система дедупликации на основе нормализованных метаданных (название, авторы, переводчики, серия)

### 3. Проблема фрагментарности
- **Проблема**: Ознакомительные фрагменты нарушают качество поиска
- **Решение**: Интеллектуальное детектирование и карантин фрагментов на основе анализа содержимого

### 4. Проблема разнородности форматов
- **Проблема**: Книги поступают в различных форматах (FB2, EPUB, MOBI) с разными структурами метаданных
- **Решение**: Унифицированная каноническая модель с нормализацией всех форматов

### 5. Проблема качества метаданных
- **Проблема**: Некорректные или отсутствующие метаданные в исходных файлах
- **Решение**: Система очистки и валидации данных с восстановлением недостающих полей

## Ключевые бизнес-процессы

### Процесс обнаружения контента
**Вход**: Директории с архивами книг от различных источников  
**Выход**: Очередь задач для обработки

Система непрерывно сканирует источники на предмет новых файлов, используя эффективные алгоритмы кеширования для предотвращения повторной обработки.

### Процесс нормализации
**Вход**: Сырые файлы книг в различных форматах  
**Выход**: Унифицированная каноническая модель

Включает извлечение из архивов, парсинг метаданных, преобразование содержимого в Markdown и очистку данных.

### Процесс валидации качества
**Вход**: Каноническая модель книги  
**Выход**: Решение о принятии/карантине

Автоматическая проверка на фрагментарность с отправкой в карантин при обнаружении, затем проверка дубликатов и соответствие критериям качества.

### Процесс индексации
**Вход**: Валидированная каноническая модель  
**Выход**: Записи в БД и очередь для RAG-обработки

Сохранение метаданных в PostgreSQL и постановка задач для последующей RAG-обработки.

## Бизнес-правила и ограничения

### Правила дедупликации
- Сравнение по единому хэшу нормализованных метаданных (metadata_hash)
- Автоматическое исключение дубликатов при обнаружении
- Сохранение информации об источниках для аудита

### Правила качества контента
- Отклонение ознакомительных фрагментов для предотвращения загрязнения базы (полное исключение из обработки)
- Валидация структуры и полноты метаданных
- Нормализация авторских данных и жанровой классификации

### Правила обработки ошибок
- Карантин файлов с критическими ошибками и ознакомительных фрагментов
- Retry-логика для временных сбоев
- Детальное логирование для диагностики проблем

## Метрики успеха

### Операционные метрики
- Пропускная способность: количество обработанных книг в час
- Коэффициент дедупликации: процент отсеянных дубликатов
- Коэффициент карантина: процент файлов отправленных в карантин (фрагменты + ошибки)
- Качество данных: процент успешно обработанных файлов

### Бизнес-метрики
- Полнота коллекции: покрытие книг по различным источникам
- Качество метаданных: процент книг с полными метаданными
- Готовность для RAG: количество книг, готовых для индексации в векторной БД

## Экономическая модель

Система ориентирована на снижение операционных затрат при работе с большими литературными коллекциями:

- **Автоматизация**: Устранение ручного труда при обработке массивов данных
- **Масштабируемость**: Линейное увеличение производительности при добавлении воркеров
- **Качество**: Уменьшение количества нерелевантных результатов в downstream-системах
- **Переиспользование**: Унифицированный API для различных типов потребителей данных

## Интеграционные точки

### Входные интеграции
- Файловые системы с архивами книг
- Периодические дампы от различных источников
- API для добавления новых источников

### Выходные интеграции  
- PostgreSQL для метаданных и отношений
- Redis-очереди для RAG-обработки
- Файловая система для канонических JSON-представлений
- Мониторинг и алертинг систем 