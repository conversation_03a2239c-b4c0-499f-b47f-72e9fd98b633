# ПРОЕКТ "ЛИТЕРАТУРНЫЙ КОМПАС" - КРАТКОЕ ОПИСАНИЕ

## 🎯 ВИДЕНИЕ ПРОЕКТА
Создание автоматизированной системы для обработки, анализа и индексации книг из различных источников с целью построения "литературного компаса" для навигации по литературному контенту.

## 🏗️ АРХИТЕКТУРА СИСТЕМЫ

### Основные компоненты:
1. **Сканер-продюсер** (\`run_10_scan_sources.py\`) - поиск новых файлов в источниках
2. **Воркер-потребитель** (\`run_20_process_book_worker.py\`) - обработка с модульным парсингом
3. **Система восстановления** (\`run_00_recovery.py\`) - мониторинг и восстановление

### Пайплайн обработки:
- **Parsing Pipeline (20)** - извлечение метаданных и содержимого
- **Chunking Pipeline (30)** - разбивка на смысловые фрагменты
- **Enrichment Pipeline (40)** - обогащение метаданных через NLU/NER
- **Vectorization Pipeline (50)** - создание векторных представлений

## 💾 ТЕХНОЛОГИЧЕСКИЙ СТЕК

### Основные технологии:
- **Python 3.12** - основной язык разработки
- **PostgreSQL** - основная БД для хранения данных
- **Redis** - очереди задач и кэширование
- **Ruff + Mypy** - линтинг и проверка типов

### Архитектурные принципы:
- **Модульная архитектура** - четкое разделение ответственности
- **Асинхронная обработка** - использование очередей Redis
- **Fault tolerance** - система восстановления после сбоев
- **Масштабируемость** - горизонтальное масштабирование воркеров

## 📊 ИСТОЧНИКИ ДАННЫХ

### Поддерживаемые источники:
- **Flibusta** (zip_flibusta) - source_type: 1
- **Searchfloor** (zip_searchfloor) - source_type: 2
- **Anna Archive** (zip_anna) - source_type: 3

### Поддерживаемые форматы:
- **FB2** - основной формат русскоязычных книг
- **EPUB** - международный стандарт
- **TXT** - простой текст
- **RTF** - Rich Text Format

## 🔧 СИСТЕМНЫЕ ОСОБЕННОСТИ

### Обработка файлов:
- **Карантин** - изоляция проблемных файлов
- **Дедупликация** - по хэшам MD5
- **Атомарность** - гарантия завершения операций
- **Репакинг** - оптимизация хранения

### Мониторинг:
- **Детальное логирование** - worker.log с полной трассировкой
- **Статистика** - метрики обработки
- **Восстановление** - автоматическое восстановление после сбоев

## 🎯 ТЕКУЩИЙ СТАТУС
Проект находится в активной разработке с функционирующим пайплайном обработки parsing stage. Следующие этапы - RAG pipeline и система рекомендаций.

## 📋 ПРИОРИТЕТНЫЕ ЗАДАЧИ
1. Стабилизация parsing pipeline
2. Развитие RAG возможностей
3. Оптимизация производительности
4. Расширение поддерживаемых форматов
