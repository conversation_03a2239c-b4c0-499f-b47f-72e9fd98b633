# ТЕХНИЧЕСКИЙ КОНТЕКСТ - ЛИТЕРАТУРНЫЙ КОМПАС

## 💻 ТЕХНОЛОГИЧЕСКИЙ СТЕК

### Основные технологии:
- **Python 3.12** - основной язык разработки
- **PostgreSQL 15+** - основная база данных
- **Redis 7+** - очереди задач и кэширование
- **Docker** - контейнеризация сервисов
- **Git** - система контроля версий

### Библиотеки и фреймворки:
- **SQLAlchemy** - ORM для работы с БД
- **Redis-py** - клиент для Redis
- **Pydantic** - валидация данных
- **Click** - CLI интерфейс
- **Pytest** - тестирование

### Инструменты разработки:
- **Ruff** - форматирование и линтинг
- **Mypy** - проверка типов
- **Pre-commit** - хуки для качества кода
- **Docker Compose** - оркестрация контейнеров

## 🏗️ АРХИТЕКТУРА СИСТЕМЫ

### Компоненты инфраструктуры:
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Source Data   │    │   Processing    │    │    Storage      │
│  (ZIP Archives) │────│   Pipeline      │────│  (PostgreSQL)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   Redis Queue   │
                       │   Management    │
                       └─────────────────┘
```

### Слои приложения:
1. **Presentation Layer** - CLI интерфейсы
2. **Application Layer** - бизнес-логика
3. **Domain Layer** - доменные модели
4. **Infrastructure Layer** - внешние сервисы

## 🔧 КОНФИГУРАЦИЯ И НАСТРОЙКИ

### Переменные окружения:
- **DATABASE_URL** - подключение к PostgreSQL
- **REDIS_URL** - подключение к Redis
- **SOURCE_DIRS** - директории источников
- **CANONICAL_STORAGE_PATH** - путь для сохранения книг

### Конфигурационные файлы:
- **pyproject.toml** - настройки проекта
- **docker-compose.yml** - оркестрация сервисов
- **.env** - переменные окружения
- **requirements.txt** - Python зависимости

## 📊 СХЕМА ДАННЫХ

### Основные таблицы:
- **books** - метаданные книг
- **authors** - информация об авторах
- **genres** - жанры произведений
- **sources** - информация об источниках
- **processing_logs** - журнал обработки

### Очереди Redis:
- **books:queue:20_parsing_new** - новые задачи парсинга
- **books:queue:30_chunking_new** - задачи разбивки
- **books:queue:40_enrich_new** - задачи обогащения
- **books:queue:50_vectorize_new** - задачи векторизации

## 🚀 РАЗВЕРТЫВАНИЕ И ЭКСПЛУАТАЦИЯ

### Среды разработки:
- **Local** - локальная разработка
- **Docker** - контейнеризованная среда
- **Testing** - автоматическое тестирование
- **Production** - промышленная эксплуатация

### Мониторинг:
- **Логирование** - структурированные логи
- **Метрики** - производительность системы
- **Алерты** - уведомления о проблемах
- **Дашборды** - визуализация данных

## 🔒 БЕЗОПАСНОСТЬ И КАЧЕСТВО

### Практики безопасности:
- **Input Validation** - валидация входных данных
- **SQL Injection Prevention** - параметризованные запросы
- **File System Security** - безопасная работа с файлами
- **Resource Limits** - ограничения ресурсов

### Контроль качества:
- **Code Review** - ревью кода
- **Automated Testing** - автоматические тесты
- **Static Analysis** - статический анализ
- **Performance Testing** - тестирование производительности

## 📋 ТЕХНИЧЕСКИЕ ТРЕБОВАНИЯ

### Системные требования:
- **CPU**: 4+ cores для воркеров
- **RAM**: 8+ GB для полного пайплайна
- **Storage**: 1+ TB для книг и индексов
- **Network**: стабильное соединение

### Требования к производительности:
- **Throughput**: 1000+ книг/час
- **Latency**: <100ms для поиска
- **Availability**: 99.9% uptime
- **Scalability**: горизонтальное масштабирование

## 🔄 ИНТЕГРАЦИИ И API

### Внешние интеграции:
- **NLP Services** - для анализа текста
- **Storage Services** - для долговременного хранения
- **Monitoring Services** - для мониторинга
- **Notification Services** - для уведомлений

### API endpoints:
- **Search API** - поиск по книгам
- **Metadata API** - получение метаданных
- **Admin API** - административные операции
- **Monitoring API** - метрики и статус
